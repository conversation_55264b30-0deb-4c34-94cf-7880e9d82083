<template>
	<!-- 弹窗遮罩层 -->
	<view v-if="visible" class="picker-overlay" @click="onOverlayClick">
		<view class="picker-modal" @click.stop="">
			<view class="calendar-picker-container">
				<!-- 导航栏 -->
				<view class="navbar">
					<text class="nav-btn cancel-btn" @click="onCancel">取消</text>
					<text class="nav-title">选择日期</text>
					<view class="confirm-btn-container">
						<text class="nav-btn confirm-btn" @click="onConfirm">确定</text>
					</view>
				</view>

				<!-- 年月控制区域 -->
				<view class="calendar-header">
					<view class="month-nav-btn" @click="prevMonth">
						<text class="nav-arrow">‹</text>
					</view>
					<view class="year-month-display" @click="openYearMonthPicker">
						<text class="year-month-text">{{ currentYearMonth }}</text>
					</view>
					<view class="month-nav-btn" @click="nextMonth">
						<text class="nav-arrow">›</text>
					</view>
				</view>

				<!-- 星期标题 -->
				<view class="week-header">
					<view v-for="(day, index) in weekDays" :key="index" class="week-day">
						<text class="week-day-text">{{ day }}</text>
					</view>
				</view>

				<!-- 日历网格 -->
				<view class="calendar-grid">
					<view v-for="(week, weekIndex) in weeks" :key="weekIndex" class="calendar-week">
						<view v-for="(day, dayIndex) in week" :key="dayIndex" 
							class="calendar-day"
							:class="{
								'day-disabled': day.disabled,
								'day-today': day.is_today,
								'day-selected': isSelectedDay(day)
							}"
							@click="onDaySelect(day)">
							<text class="day-number" 
								:class="{
									'day-number-disabled': day.disabled,
									'day-number-today': day.is_today,
									'day-number-selected': isSelectedDay(day)
								}">{{ day.date }}</text>
							<text class="day-lunar"
								:class="{
									'day-lunar-disabled': day.disabled,
									'day-lunar-today': day.is_today,
									'day-lunar-selected': isSelectedDay(day)
								}">{{ day.lunar }}</text>
						</view>
					</view>
				</view>

				<!-- 当前选择显示 -->
				<view class="current-selection">
					<text class="selection-label">当前选择：</text>
					<text class="selection-value">{{ selectedDateText }}</text>
				</view>
			</view>
		</view>
	</view>

	<!-- 年月选择器 -->
	<main-yearmonth-picker 
		ref="yearmonthPicker" 
		:initial-year="currentYear"
		:initial-month="currentMonth"
		@confirm="onYearMonthConfirm" 
		@cancel="onYearMonthCancel">
	</main-yearmonth-picker>
</template>

<script>
	import { Calendar, DateType } from '@/utils/calendar.uts'
	import MainYearmonthPicker from './main-form/tools/main-yearmonth-picker.uvue'

	export default {
		name: "main-calendar-picker",
		components: {
			MainYearmonthPicker
		},
		emits: ['cancel', 'confirm'],
		props: {
			// 初始日期 (YYYY-MM-DD格式)
			initialDate: {
				type: String,
				default: () => {
					const today = new Date()
					const year = today.getFullYear()
					const month = today.getMonth() + 1
					const date = today.getDate()
					return `${year}-${month < 10 ? '0' + month : month}-${date < 10 ? '0' + date : date}`
				}
			}
		},
		data() {
			return {
				// 控制弹窗显示
				visible: false as boolean,
				// 日历工具类实例
				calendar: new Calendar() as Calendar,
				// 当前年份
				currentYear: new Date().getFullYear() as number,
				// 当前月份
				currentMonth: (new Date().getMonth() + 1) as number,
				// 当前选中的日期
				selectedDate: "" as string,
				// 日历周数据
				weeks: [] as Array<Array<DateType>>,
				// 星期标题
				weekDays: ['一', '二', '三', '四', '五', '六', '日'] as string[]
			}
		},
		computed: {
			// 当前年月显示文本
			currentYearMonth(): string {
				return this.calendar.formatYearMonth(this.currentYear, this.currentMonth)
			},
			// 选中日期显示文本
			selectedDateText(): string {
				if (this.selectedDate === "") {
					return "未选择"
				}
				const parts = this.selectedDate.split('-')
				if (parts.length == 3) {
					return `${parts[0]}年${parseInt(parts[1])}月${parseInt(parts[2])}日`
				}
				return this.selectedDate
			}
		},
		created() {
			this.initializeData()
		},
		methods: {
			// 初始化数据
			initializeData() {
				// 解析初始日期
				const dateParts = this.initialDate.split('-')
				if (dateParts.length == 3) {
					this.currentYear = parseInt(dateParts[0])
					this.currentMonth = parseInt(dateParts[1])
					this.selectedDate = this.initialDate
				}
				this.updateCalendar()
			},

			// 更新日历数据
			updateCalendar() {
				const dateStr = `${this.currentYear}-${this.currentMonth}-1`
				this.weeks = this.calendar.getWeeks(dateStr)
			},

			// 判断是否为选中日期
			isSelectedDay(day: DateType): boolean {
				if (this.selectedDate === "" || day.disabled) {
					return false
				}
				return day.fullDate === this.selectedDate
			},

			// 日期选择事件
			onDaySelect(day: DateType) {
				if (day.disabled) {
					return
				}
				this.selectedDate = day.fullDate
			},

			// 上个月
			prevMonth() {
				if (this.currentMonth == 1) {
					this.currentYear-- 
					this.currentMonth = 12
				} else {
					this.currentMonth--
				}
				this.updateCalendar()
			},

			// 下个月
			nextMonth() {
				if (this.currentMonth == 12) {
					this.currentYear++
					this.currentMonth = 1
				} else {
					this.currentMonth++
				}
				this.updateCalendar()
			},

			// 打开年月选择器
			openYearMonthPicker() {
				const yearmonthPicker = this.$refs["yearmonthPicker"] as ComponentPublicInstance
				yearmonthPicker.$callMethod("open")
			},

			// 年月选择确认
			onYearMonthConfirm(yearMonthData: UTSJSONObject) {
				const year = yearMonthData.getNumber("year")
				const month = yearMonthData.getNumber("month")
				
				if (year != null && month != null) {
					this.currentYear = year
					this.currentMonth = month
					this.updateCalendar()
				}
			},

			// 年月选择取消
			onYearMonthCancel() {
				// 取消选择，不做任何操作
			},

			// 打开弹窗
			open() {
				this.visible = true
			},

			// 关闭弹窗
			close() {
				this.visible = false
			},

			// 点击遮罩层关闭弹窗
			onOverlayClick() {
				this.close()
				this.$emit('cancel')
			},

			// 取消按钮点击事件
			onCancel() {
				this.close()
				this.$emit('cancel')
			},

			// 确定按钮点击事件
			onConfirm() {
				if (this.selectedDate === "") {
					uni.showToast({
						title: '请选择日期',
						icon: 'none'
					})
					return
				}
				
				this.close()
				this.$emit('confirm', {
					date: this.selectedDate,
					year: this.currentYear,
					month: this.currentMonth,
					day: parseInt(this.selectedDate.split('-')[2])
				})
			}
		}
	}
</script>

<style>
	/* 弹窗遮罩层 */
	.picker-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.picker-modal {
		width: 90%;
		max-width: 700rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	}

	.calendar-picker-container {
		width: 100%;
		background-color: #ffffff;
		display: flex;
		flex-direction: column;
	}

	/* 导航栏样式 */
	.navbar {
		height: 44px;
		background-color: #f8f8f8;
		border-bottom: 1px solid #e5e5e5;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 0 10px;
	}

	.nav-btn {
		font-size: 16px;
		color: #007aff;
		padding: 8px 12px;
	}

	.cancel-btn {
		color: #999999;
	}

	.confirm-btn-container {
		height: 30px;
		background-color: #007aff;
		border-radius: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
	}

	.confirm-btn {
		color: #ffffff;
		font-weight: bold;
	}

	.nav-title {
		font-size: 17px;
		color: #333333;
	}

	/* 年月控制区域 */
	.calendar-header {
		height: 80rpx;
		background-color: #f8f9fa;
		border-bottom: 1px solid #e5e5e5;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
	}

	.month-nav-btn {
		width: 60rpx;
		height: 60rpx;
		background-color: rgba(0, 122, 255, 0.1);
		border-radius: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.nav-arrow {
		font-size: 32rpx;
		color: #007aff;
		font-weight: bold;
	}

	.year-month-display {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0 20rpx;
	}

	.year-month-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}

	/* 星期标题 */
	.week-header {
		height: 60rpx;
		background-color: #f0f0f0;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.week-day {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.week-day-text {
		font-size: 24rpx;
		color: #666666;
		font-weight: bold;
	}

	/* 日历网格 */
	.calendar-grid {
		padding: 10rpx;
		background-color: #ffffff;
	}

	.calendar-week {
		display: flex;
		flex-direction: row;
		margin-bottom: 8rpx;
	}

	.calendar-day {
		flex: 1;
		height: 80rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin: 0 2rpx;
		border-radius: 8rpx;
		position: relative;
	}

	.calendar-day.day-today {
		background-color: rgba(255, 69, 58, 0.1);
		border: 2rpx solid #ff453a;
	}

	.calendar-day.day-selected {
		background-color: #007aff;
		transform: scale(1.05);
	}

	.calendar-day.day-disabled {
		opacity: 0.3;
	}

	.day-number {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		line-height: 1;
	}

	.day-number-today {
		color: #ff453a;
	}

	.day-number-selected {
		color: #ffffff;
	}

	.day-number-disabled {
		color: #cccccc;
	}

	.day-lunar {
		font-size: 20rpx;
		color: #999999;
		line-height: 1;
		margin-top: 4rpx;
	}

	.day-lunar-today {
		color: #ff453a;
	}

	.day-lunar-selected {
		color: #ffffff;
	}

	.day-lunar-disabled {
		color: #cccccc;
	}

	/* 当前选择显示区域 */
	.current-selection {
		padding: 20rpx;
		background-color: #f8f9fa;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		border-top: 1px solid #e5e5e5;
	}

	.selection-label {
		font-size: 28rpx;
		color: #666666;
		margin-right: 10rpx;
	}

	.selection-value {
		font-size: 32rpx;
		color: #007aff;
		font-weight: bold;
	}
</style>
