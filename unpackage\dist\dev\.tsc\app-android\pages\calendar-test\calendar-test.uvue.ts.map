{"version": 3, "file": "pages/calendar-test/calendar-test.uvue", "names": [], "sources": ["pages/calendar-test/calendar-test.uvue"], "sourcesContent": ["<template>\n\n\t<scroll-view class=\"container\">\n\n\t\t<view class=\"content\">\n\t\t\t<text class=\"title\">日历弹窗测试</text>\n\t\t\t\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<button class=\"test-btn\" @click=\"openCalendarPicker\">打开日历选择器</button>\n\t\t\t\t\n\t\t\t\t<view class=\"result-section\">\n\t\t\t\t\t<text class=\"result-label\">选择的日期：</text>\n\t\t\t\t\t<text class=\"result-value\">{{ selectedDate  }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 日历选择器 -->\n\t\t<main-calendar-picker \n\t\t\tref=\"calendarPicker\" \n\t\t\t:initial-date=\"initialDate\"\n\t\t\t@confirm=\"onCalendarConfirm\" \n\t\t\t@cancel=\"onCalendarCancel\">\n\t\t</main-calendar-picker>\n\n\t</scroll-view>\n\n</template>\n\n<script>\n\timport MainCalendarPicker from '@/components/main-calendar-picker.uvue'\n\n\texport default {\n\t\tname: \"calendar-test\",\n\t\tcomponents: {\n\t\t\tMainCalendarPicker\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 初始日期\n\t\t\t\tinitialDate: \"\" as string,\n\t\t\t\t// 选择的日期\n\t\t\t\tselectedDate: \"\" as string\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 设置初始日期为今天\n\t\t\tconst today = new Date()\n\t\t\tconst year = today.getFullYear()\n\t\t\tconst month = today.getMonth() + 1\n\t\t\tconst date = today.getDate()\n\t\t\tthis.initialDate = `${year}-${month < 10 ? '0' + month : month}-${date < 10 ? '0' + date : date}`\n\t\t},\n\t\tmethods: {\n\t\t\t// 打开日历选择器\n\t\t\topenCalendarPicker() {\n\t\t\t\tconst calendarPicker = this.$refs[\"calendarPicker\"] as ComponentPublicInstance\n\t\t\t\tcalendarPicker.$callMethod(\"open\")\n\t\t\t},\n\n\t\t\t// 日历选择确认\n\t\t\tonCalendarConfirm(dateData: UTSJSONObject) {\n\t\t\t\tconsole.log('选择的日期:', dateData)\n\t\t\t\tconst date = dateData.getString(\"date\")\n\t\t\t\tif (date != null) {\n\t\t\t\t\tthis.selectedDate = date\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `已选择: ${this.selectedDate}`,\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 日历选择取消\n\t\t\tonCalendarCancel() {\n\t\t\t\tconsole.log('取消选择日期')\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '取消选择',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tflex: 1;\n\t\tbackground-color: #f5f5f5;\n\t}\n\n\t.content {\n\t\tpadding: 40rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\n\t.title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 60rpx;\n\t}\n\n\t.test-section {\n\t\twidth: 100%;\n\t\tmax-width: 600rpx;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.test-btn {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tbackground-color: #007aff;\n\t\tcolor: #ffffff;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tborder: none;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.result-section {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.result-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.result-value {\n\t\tfont-size: 36rpx;\n\t\tcolor: #007aff;\n\t\tfont-weight: bold;\n\t}\n</style>\n"], "mappings": ";CA8BC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;CAEtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GAC1B;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;GACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjG,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAClC,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;KACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACxB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;GACF,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;GACF;EACD;CACD;;;;;;;;SAjFA,IAuBc,qBAvBD,KAAK,EAAC,WAAW;IAE7B,IAWO,cAXD,KAAK,EAAC,SAAS;MACpB,IAAiC,cAA3B,KAAK,EAAC,OAAO,KAAC,QAAM;MAE1B,IAOO,cAPD,KAAK,EAAC,cAAc;QACzB,IAAqE;UAA7D,KAAK,EAAC,UAAU;UAAE,OAAK,EAAE,uBAAkB;YAAE,SAAO;QAE5D,IAGO,cAHD,KAAK,EAAC,gBAAgB;UAC3B,IAAwC,cAAlC,KAAK,EAAC,cAAc,KAAC,QAAM;UACjC,IAAqD,cAA/C,KAAK,EAAC,cAAc,SAAI,iBAAY;;;;IAM7C,IAKuB;MAJtB,GAAG,EAAC,gBAAgB;MACnB,cAAY,EAAE,gBAAW;MACzB,SAAO,EAAE,sBAAiB;MAC1B,QAAM,EAAE,qBAAgB"}