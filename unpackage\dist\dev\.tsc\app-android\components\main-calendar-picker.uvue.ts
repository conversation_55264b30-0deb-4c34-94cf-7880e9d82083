
	import { Calendar, DateType } from '@/utils/calendar.uts'
	import MainYearmonthPicker from './main-form/tools/main-yearmonth-picker.uvue'

	const __sfc__ = defineComponent({
		name: "main-calendar-picker",
		components: {
			MainYearmonthPicker
		},
		emits: ['cancel', 'confirm'],
		props: {
			// 初始日期 (YYYY-MM-DD格式)
			initialDate: {
				type: String,
				default: () => {
					const today = new Date()
					const year = today.getFullYear()
					const month = today.getMonth() + 1
					const date = today.getDate()
					return `${year}-${month < 10 ? '0' + month : month}-${date < 10 ? '0' + date : date}`
				}
			}
		},
		data() {
			return {
				// 控制弹窗显示
				visible: false as boolean,
				// 日历工具类实例
				calendar: new Calendar() as Calendar,
				// 当前年份
				currentYear: new Date().getFullYear() as number,
				// 当前月份
				currentMonth: (new Date().getMonth() + 1) as number,
				// 当前选中的日期
				selectedDate: "" as string,
				// 日历周数据
				weeks: [] as Array<Array<DateType>>,
				// 星期标题
				weekDays: ['一', '二', '三', '四', '五', '六', '日'] as string[]
			}
		},
		computed: {
			// 当前年月显示文本
			currentYearMonth(): string {
				return this.calendar.formatYearMonth(this.currentYear, this.currentMonth)
			},
			// 选中日期显示文本
			selectedDateText(): string {
				if (this.selectedDate === "") {
					return "未选择"
				}
				const parts = this.selectedDate.split('-')
				if (parts.length == 3) {
					return `${parts[0]}年${parseInt(parts[1])}月${parseInt(parts[2])}日`
				}
				return this.selectedDate
			}
		},
		created() {
			this.initializeData()
		},
		methods: {
			// 初始化数据
			initializeData() {
				// 解析初始日期
				const dateParts = this.initialDate.split('-')
				if (dateParts.length == 3) {
					this.currentYear = parseInt(dateParts[0])
					this.currentMonth = parseInt(dateParts[1])
					this.selectedDate = this.initialDate
				}
				this.updateCalendar()
			},

			// 更新日历数据
			updateCalendar() {
				const dateStr = `${this.currentYear}-${this.currentMonth}-1`
				this.weeks = this.calendar.getWeeks(dateStr)
				console.log(this.weeks, " at components/main-calendar-picker.uvue:159")
			},

			// 判断是否为选中日期
			isSelectedDay(day: DateType): boolean {
				if (this.selectedDate === "" || day.disabled) {
					return false
				}
				return day.fullDate === this.selectedDate
			},

			// 日期选择事件
			onDaySelect(day: DateType) {
				if (day.disabled) {
					return
				}
				this.selectedDate = day.fullDate
			},

			// 上个月
			prevMonth() {
				if (this.currentMonth == 1) {
					this.currentYear-- 
					this.currentMonth = 12
				} else {
					this.currentMonth--
				}
				this.updateCalendar()
			},

			// 下个月
			nextMonth() {
				if (this.currentMonth == 12) {
					this.currentYear++
					this.currentMonth = 1
				} else {
					this.currentMonth++
				}
				this.updateCalendar()
			},

			// 打开年月选择器
			openYearMonthPicker() {
				const yearmonthPicker = this.$refs["yearmonthPicker"] as ComponentPublicInstance
				yearmonthPicker.$callMethod("open")
			},

			// 年月选择确认
			onYearMonthConfirm(yearMonthData: UTSJSONObject) {
				const year = yearMonthData.getNumber("year")
				const month = yearMonthData.getNumber("month")
				
				if (year != null && month != null) {
					this.currentYear = year
					this.currentMonth = month
					this.updateCalendar()
				}
			},

			// 年月选择取消
			onYearMonthCancel() {
				// 取消选择，不做任何操作
			},

			// 打开弹窗
			open() {
				this.visible = true
			},

			// 关闭弹窗
			close() {
				this.visible = false
			},

			// 点击遮罩层关闭弹窗
			onOverlayClick() {
				this.close()
				this.$emit('cancel')
			},

			// 取消按钮点击事件
			onCancel() {
				this.close()
				this.$emit('cancel')
			},

			// 确定按钮点击事件
			onConfirm() {
				if (this.selectedDate === "") {
					uni.showToast({
						title: '请选择日期',
						icon: 'none'
					})
					return
				}

				// 找到选中的日期对象
				let selectedDateObj: DateType | null = null
				for (let week of this.weeks) {
					for (let day of week) {
						if (day.fullDate === this.selectedDate) {
							selectedDateObj = day as DateType
							break
						}
					}
					if (selectedDateObj != null) {
						break
					}
				}

				this.close()

				// 返回完整的日期信息
				if (selectedDateObj != null) {
					this.$emit('confirm', selectedDateObj)
				} else {
					// 备用方案，返回基本信息
					this.$emit('confirm', {
						date: this.selectedDate,
						year: this.currentYear,
						month: this.currentMonth,
						day: parseInt(this.selectedDate.split('-')[2])
					})
				}
			}
		}
	})

export default __sfc__
function GenComponentsMainCalendarPickerRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_main_yearmonth_picker = resolveComponent("main-yearmonth-picker")

  return _cE(Fragment, null, [
    isTrue(_ctx.visible)
      ? _cE("view", _uM({
          key: 0,
          class: "picker-overlay",
          onClick: _ctx.onOverlayClick
        }), [
          _cE("view", _uM({
            class: "picker-modal",
            onClick: withModifiers(() => {}, ["stop"])
          }), [
            _cE("view", _uM({ class: "calendar-picker-container" }), [
              _cE("view", _uM({ class: "navbar" }), [
                _cE("text", _uM({
                  class: "nav-btn cancel-btn",
                  onClick: _ctx.onCancel
                }), "取消", 8 /* PROPS */, ["onClick"]),
                _cE("text", _uM({ class: "nav-title" }), "选择日期"),
                _cE("view", _uM({ class: "confirm-btn-container" }), [
                  _cE("text", _uM({
                    class: "nav-btn confirm-btn",
                    onClick: _ctx.onConfirm
                  }), "确定", 8 /* PROPS */, ["onClick"])
                ])
              ]),
              _cE("view", _uM({ class: "calendar-header" }), [
                _cE("view", _uM({
                  class: "month-nav-btn",
                  onClick: _ctx.prevMonth
                }), [
                  _cE("text", _uM({ class: "nav-arrow" }), "‹")
                ], 8 /* PROPS */, ["onClick"]),
                _cE("view", _uM({
                  class: "year-month-display",
                  onClick: _ctx.openYearMonthPicker
                }), [
                  _cE("text", _uM({ class: "year-month-text" }), _tD(_ctx.currentYearMonth), 1 /* TEXT */)
                ], 8 /* PROPS */, ["onClick"]),
                _cE("view", _uM({
                  class: "month-nav-btn",
                  onClick: _ctx.nextMonth
                }), [
                  _cE("text", _uM({ class: "nav-arrow" }), "›")
                ], 8 /* PROPS */, ["onClick"])
              ]),
              _cE("view", _uM({ class: "week-header" }), [
                _cE(Fragment, null, RenderHelpers.renderList(_ctx.weekDays, (day, index, __index, _cached): any => {
                  return _cE("view", _uM({
                    key: index,
                    class: "week-day"
                  }), [
                    _cE("text", _uM({ class: "week-day-text" }), _tD(day), 1 /* TEXT */)
                  ])
                }), 128 /* KEYED_FRAGMENT */)
              ]),
              _cE("view", _uM({ class: "calendar-grid" }), [
                _cE(Fragment, null, RenderHelpers.renderList(_ctx.weeks, (week, weekIndex, __index, _cached): any => {
                  return _cE("view", _uM({
                    key: weekIndex,
                    class: "calendar-week"
                  }), [
                    _cE(Fragment, null, RenderHelpers.renderList(week, (day, dayIndex, __index, _cached): any => {
                      return _cE("view", _uM({
                        key: dayIndex,
                        class: _nC(["calendar-day", _uM({
								'day-disabled': day.disabled,
								'day-today': day.is_today,
								'day-selected': _ctx.isSelectedDay(day)
							})]),
                        onClick: () => {_ctx.onDaySelect(day)}
                      }), [
                        _cE("text", _uM({
                          class: _nC(["day-number", _uM({
									'day-number-disabled': day.disabled,
									'day-number-today': day.is_today,
									'day-number-selected': _ctx.isSelectedDay(day)
								})])
                        }), _tD(day.date), 3 /* TEXT, CLASS */),
                        _cE("text", _uM({
                          class: _nC(["day-lunar", _uM({
									'day-lunar-disabled': day.disabled,
									'day-lunar-today': day.is_today,
									'day-lunar-selected': _ctx.isSelectedDay(day)
								})])
                        }), _tD(day.lunar), 3 /* TEXT, CLASS */)
                      ], 10 /* CLASS, PROPS */, ["onClick"])
                    }), 128 /* KEYED_FRAGMENT */)
                  ])
                }), 128 /* KEYED_FRAGMENT */)
              ]),
              _cE("view", _uM({ class: "current-selection" }), [
                _cE("text", _uM({ class: "selection-label" }), "当前选择："),
                _cE("text", _uM({ class: "selection-value" }), _tD(_ctx.selectedDateText), 1 /* TEXT */)
              ])
            ])
          ], 8 /* PROPS */, ["onClick"])
        ], 8 /* PROPS */, ["onClick"])
      : _cC("v-if", true),
    _cV(_component_main_yearmonth_picker, _uM({
      ref: "yearmonthPicker",
      "initial-year": _ctx.currentYear,
      "initial-month": _ctx.currentMonth,
      onConfirm: _ctx.onYearMonthConfirm,
      onCancel: _ctx.onYearMonthCancel
    }), null, 8 /* PROPS */, ["initial-year", "initial-month", "onConfirm", "onCancel"])
  ], 64 /* STABLE_FRAGMENT */)
}
const GenComponentsMainCalendarPickerStyles = [_uM([["picker-overlay", _pS(_uM([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(0,0,0,0.5)"], ["display", "flex"], ["alignItems", "center"], ["justifyContent", "center"], ["zIndex", 1000]]))], ["picker-modal", _pS(_uM([["width", "90%"], ["maxWidth", "700rpx"], ["backgroundColor", "#ffffff"], ["borderTopLeftRadius", "20rpx"], ["borderTopRightRadius", "20rpx"], ["borderBottomRightRadius", "20rpx"], ["borderBottomLeftRadius", "20rpx"], ["overflow", "hidden"], ["boxShadow", "0 8px 32px rgba(0, 0, 0, 0.3)"]]))], ["calendar-picker-container", _pS(_uM([["width", "100%"], ["backgroundColor", "#ffffff"], ["display", "flex"], ["flexDirection", "column"]]))], ["navbar", _pS(_uM([["height", 44], ["backgroundColor", "#f8f8f8"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#e5e5e5"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["justifyContent", "space-between"], ["paddingTop", 0], ["paddingRight", 10], ["paddingBottom", 0], ["paddingLeft", 10]]))], ["nav-btn", _pS(_uM([["fontSize", 16], ["color", "#007aff"], ["paddingTop", 8], ["paddingRight", 12], ["paddingBottom", 8], ["paddingLeft", 12]]))], ["cancel-btn", _pS(_uM([["color", "#999999"]]))], ["confirm-btn-container", _pS(_uM([["height", 30], ["backgroundColor", "#007aff"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["boxShadow", "0 2rpx 8rpx rgba(0, 122, 255, 0.3)"]]))], ["confirm-btn", _pS(_uM([["color", "#ffffff"], ["fontWeight", "bold"]]))], ["nav-title", _pS(_uM([["fontSize", 17], ["color", "#333333"]]))], ["calendar-header", _pS(_uM([["height", "80rpx"], ["backgroundColor", "#f8f9fa"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#e5e5e5"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["justifyContent", "space-between"], ["paddingTop", 0], ["paddingRight", "20rpx"], ["paddingBottom", 0], ["paddingLeft", "20rpx"]]))], ["month-nav-btn", _pS(_uM([["width", "60rpx"], ["height", "60rpx"], ["backgroundColor", "rgba(0,122,255,0.1)"], ["borderTopLeftRadius", "30rpx"], ["borderTopRightRadius", "30rpx"], ["borderBottomRightRadius", "30rpx"], ["borderBottomLeftRadius", "30rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"]]))], ["nav-arrow", _pS(_uM([["fontSize", "32rpx"], ["color", "#007aff"], ["fontWeight", "bold"]]))], ["year-month-display", _pS(_uM([["flex", 1], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["paddingTop", 0], ["paddingRight", "20rpx"], ["paddingBottom", 0], ["paddingLeft", "20rpx"]]))], ["year-month-text", _pS(_uM([["fontSize", "36rpx"], ["fontWeight", "bold"], ["color", "#333333"]]))], ["week-header", _pS(_uM([["height", "60rpx"], ["backgroundColor", "#f0f0f0"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"]]))], ["week-day", _pS(_uM([["flex", 1], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"]]))], ["week-day-text", _pS(_uM([["fontSize", "24rpx"], ["color", "#666666"], ["fontWeight", "bold"]]))], ["calendar-grid", _pS(_uM([["paddingTop", "10rpx"], ["paddingRight", "10rpx"], ["paddingBottom", "10rpx"], ["paddingLeft", "10rpx"], ["backgroundColor", "#ffffff"]]))], ["calendar-week", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["marginBottom", "8rpx"]]))], ["calendar-day", _uM([["", _uM([["flex", 1], ["height", "80rpx"], ["display", "flex"], ["flexDirection", "column"], ["justifyContent", "center"], ["alignItems", "center"], ["marginTop", 0], ["marginRight", "2rpx"], ["marginBottom", 0], ["marginLeft", "2rpx"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["position", "relative"]])], [".day-today", _uM([["backgroundColor", "rgba(255,69,58,0.1)"], ["borderTopWidth", "2rpx"], ["borderRightWidth", "2rpx"], ["borderBottomWidth", "2rpx"], ["borderLeftWidth", "2rpx"], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#ff453a"], ["borderRightColor", "#ff453a"], ["borderBottomColor", "#ff453a"], ["borderLeftColor", "#ff453a"]])], [".day-selected", _uM([["backgroundColor", "#007aff"], ["transform", "scale(1.05)"]])], [".day-disabled", _uM([["opacity", 0.3]])]])], ["day-number", _pS(_uM([["fontSize", "28rpx"], ["color", "#333333"], ["fontWeight", "bold"], ["lineHeight", 1]]))], ["day-number-today", _pS(_uM([["color", "#ff453a"]]))], ["day-number-selected", _pS(_uM([["color", "#ffffff"]]))], ["day-number-disabled", _pS(_uM([["color", "#cccccc"]]))], ["day-lunar", _pS(_uM([["fontSize", "20rpx"], ["color", "#999999"], ["lineHeight", 1], ["marginTop", "4rpx"]]))], ["day-lunar-today", _pS(_uM([["color", "#ff453a"]]))], ["day-lunar-selected", _pS(_uM([["color", "#ffffff"]]))], ["day-lunar-disabled", _pS(_uM([["color", "#cccccc"]]))], ["current-selection", _pS(_uM([["paddingTop", "20rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "20rpx"], ["paddingLeft", "20rpx"], ["backgroundColor", "#f8f9fa"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["justifyContent", "center"], ["borderTopWidth", 1], ["borderTopStyle", "solid"], ["borderTopColor", "#e5e5e5"]]))], ["selection-label", _pS(_uM([["fontSize", "28rpx"], ["color", "#666666"], ["marginRight", "10rpx"]]))], ["selection-value", _pS(_uM([["fontSize", "32rpx"], ["color", "#007aff"], ["fontWeight", "bold"]]))]])]
