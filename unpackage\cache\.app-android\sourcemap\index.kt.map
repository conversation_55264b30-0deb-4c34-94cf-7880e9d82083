{"version": 3, "sources": ["../../../../../../../../Soft/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/socket.ts", "App.uvue", "../../../../../../../../Soft/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts", "components/main-form/form_type.uts", "components/main-color-picker/main-color-picker.uvue", "components/main-form/tools/main-color-picker.uvue", "components/main-form/components/form-select.uvue", "utils/calendar.uts", "main.uts"], "sourcesContent": ["/// <reference types=\"@dcloudio/uni-app-x/types/uni/global\" />\n// 之所以又写了一份，是因为外层的socket，connectSocket的时候必须传入multiple:true\n// 但是android又不能传入，目前代码里又不能写条件编译之类的。\nexport function initRuntimeSocket(\n  hosts: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(null)\n  return hosts\n    .split(',')\n    .reduce<Promise<SocketTask | null>>(\n      (\n        promise: Promise<SocketTask | null>,\n        host: string\n      ): Promise<SocketTask | null> => {\n        return promise.then((socket): Promise<SocketTask | null> => {\n          if (socket != null) return Promise.resolve(socket)\n          return tryConnectSocket(host, port, id)\n        })\n      },\n      Promise.resolve(null)\n    )\n}\n\nconst SOCKET_TIMEOUT = 500\nfunction tryConnectSocket(\n  host: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  return new Promise((resolve, reject) => {\n    const socket = uni.connectSocket({\n      url: `ws://${host}:${port}/${id}`,\n      fail() {\n        resolve(null)\n      },\n    })\n    const timer = setTimeout(() => {\n      // @ts-expect-error\n      socket.close({\n        code: 1006,\n        reason: 'connect timeout',\n      } as CloseSocketOptions)\n      resolve(null)\n    }, SOCKET_TIMEOUT)\n\n    socket.onOpen((e) => {\n      clearTimeout(timer)\n      resolve(socket)\n    })\n    socket.onClose((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n    socket.onError((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n  })\n}\n", "<script lang=\"uts\">\r\n\r\n\tlet firstBackTime = 0\r\n\r\n\texport default {\r\n\t\tonLaunch: function () {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function () {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function () {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\r\n\t\tonLastPageBackPress: function () {\r\n\t\t\tconsole.log('App LastPageBackPress')\r\n\t\t\tif (firstBackTime == 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '再按一次退出应用',\r\n\t\t\t\t\tposition: 'bottom',\r\n\t\t\t\t})\r\n\t\t\t\tfirstBackTime = Date.now()\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tfirstBackTime = 0\r\n\t\t\t\t}, 2000)\r\n\t\t\t} else if (Date.now() - firstBackTime < 2000) {\r\n\t\t\t\tfirstBackTime = Date.now()\r\n\t\t\t\tuni.exit()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonExit: function () {\r\n\t\t\tconsole.log('App Exit')\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\t.uni-row {\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.uni-column {\r\n\t\tflex-direction: column;\r\n\t}\r\n</style>", "import { initRuntimeSocket } from './socket'\n\nexport function initRuntimeSocketService(): Promise<boolean> {\n  const hosts: string = process.env.UNI_SOCKET_HOSTS\n  const port: string = process.env.UNI_SOCKET_PORT\n  const id: string = process.env.UNI_SOCKET_ID\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(false)\n  let socketTask: SocketTask | null = null\n  __registerWebViewUniConsole(\n    (): string => {\n      return process.env.UNI_CONSOLE_WEBVIEW_EVAL_JS_CODE\n    },\n    (data: string) => {\n      socketTask?.send({\n        data,\n      } as SendSocketMessageOptions)\n    }\n  )\n  return Promise.resolve()\n    .then((): Promise<boolean> => {\n      return initRuntimeSocket(hosts, port, id).then((socket): boolean => {\n        if (socket == null) {\n          return false\n        }\n        socketTask = socket\n        return true\n      })\n    })\n    .catch((): boolean => {\n      return false\n    })\n}\n\ninitRuntimeSocketService()\n", "export type FormFieldData = {\r\n\tkey : string;\r\n\tname : string;\r\n\ttype : string;\r\n\tvalue : any;\r\n\tisSave ?: boolean;\r\n\tcondition ?: string;\r\n\textra : UTSJSONObject\r\n}\r\n\r\nexport type FormChangeEvent = {\r\n\tindex : number;\r\n\tvalue : any\r\n}", "<template>\r\n\t<!-- 弹窗遮罩层 -->\r\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\r\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\r\n\t\t\t<view class=\"color-picker-container\">\r\n\t\t\t\t<!-- 导航栏 -->\r\n\t\t\t\t<view class=\"navbar\">\r\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\r\n\t\t\t\t\t<text class=\"nav-title\">颜色选择</text>\r\n\t\t\t\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色系列选择按钮 -->\r\n\t\t\t\t<view class=\"color-series-section\">\r\n\t\t\t\t\t<view class=\"color-series-buttons\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(series, index) in colorSeriesList\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"series-button\"\r\n\t\t\t\t\t\t\t:class=\"{\r\n\t\t\t\t\t\t\t\t'active': selectedSeriesIndex == index,\r\n\t\t\t\t\t\t\t\t'random-button': index == 0,\r\n\t\t\t\t\t\t\t\t'normal-button': index != 0\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t:style=\"{ backgroundColor: series.color }\"\r\n\t\t\t\t\t\t\t@click=\"onSeriesSelect(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"series-text\">{{ series.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色方块列表 -->\r\n\t\t\t\t<view class=\"color-grid-section\">\r\n\t\t\t\t\t<view class=\"color-grid\">\r\n\t\t\t\t\t\t<view v-for=\"(color, index) in colorList\" :key=\"index\" class=\"color-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'selected': selectedColorIndex == index }\" :style=\"{ backgroundColor: color }\"\r\n\t\t\t\t\t\t\t@click=\"onColorSelect(index)\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 预览和透明度选择区域 -->\r\n\t\t\t\t<view class=\"preview-opacity-section\">\r\n\t\t\t\t\t<view class=\"preview-area\" @click=\"showRGBPicker\">\r\n\t\t\t\t\t\t<view class=\"preview-color\" :style=\"{ backgroundColor: finalColor }\"></view>\r\n\t\t\t\t\t\t<text class=\"rgba-text\">{{ finalColor }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"opacity-area\">\r\n\t\t\t\t\t\t<text class=\"opacity-label\">透明度</text>\r\n\t\t\t\t\t\t<view class=\"opacity-button\" @click=\"showOpacityPicker\">\r\n\t\t\t\t\t\t\t<text class=\"opacity-value\">{{ Math.round(opacity * 100) }}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- RGB设置弹窗 -->\r\n\t\t\t\t<view v-if=\"showRGBModal\" class=\"rgb-modal-overlay\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t<view class=\"rgb-modal\" @click=\"onRGBModalClick\">\r\n\t\t\t\t\t\t<view class=\"rgb-modal-header\">\r\n\t\t\t\t\t\t\t<text class=\"rgb-modal-title\">RGB颜色设置</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-preview-section\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-preview-color\" :style=\"{ backgroundColor: tempRGBColor }\"></view>\r\n\t\t\t\t\t\t\t<text class=\"rgb-preview-text\">{{ tempRGBColor }}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-controls\">\r\n\t\t\t\t\t\t\t<!-- R值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">R</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempR\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempRChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempR.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempRInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- G值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">G</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempG\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempGChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempG.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempGInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- B值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">B</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempB\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempBChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempB.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempBInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-modal-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-cancel\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">取消</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-confirm\" @click=\"confirmRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">确定</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 定义颜色类型\r\n\ttype ColorInfo = {\r\n\t\tr : number,\r\n\t\tg : number,\r\n\t\tb : number\r\n\t}\r\n\ttype RGBAValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number,\r\n\t  a: number\r\n\t}\r\n\ttype RGBValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number\r\n\t}\r\n\ttype ColorSeries = {\r\n\t  name: string,\r\n\t  color: string\r\n\t}\r\n\texport default {\r\n\t\tname: \"main-color-picker\",\r\n\t\temits: ['cancel', 'confirm'],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 控制弹窗显示\r\n\t\t\t\tvisible: false as boolean,\r\n\t\t\t\t// 当前选中的颜色系列索引\r\n\t\t\t\tselectedSeriesIndex: 0 as number,\r\n\t\t\t\t// 透明度，范围0-1\r\n\t\t\t\topacity: 1.0 as number,\r\n\t\t\t\t// 当前选中的颜色索引\r\n\t\t\t\tselectedColorIndex: 0 as number,\r\n\t\t\t\t// 基础颜色（可以根据需要修改）\r\n\t\t\t\tbaseColor: { r: 255.0, g: 0.0, b: 0.0 } as ColorInfo,\r\n\t\t\t\t// 随机色种子，用于重新生成随机色\r\n\t\t\t\trandomSeed: 0 as number,\r\n\t\t\t\t// RGB设置弹窗相关\r\n\t\t\t\tshowRGBModal: false as boolean,\r\n\t\t\t\ttempR: 255 as number,\r\n\t\t\t\ttempG: 0 as number,\r\n\t\t\t\ttempB: 0 as number,\r\n\t\t\t\t// 自定义颜色\r\n\t\t\t\tcustomColor: \"\" as string\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 颜色系列列表\r\n\t\t\tcolorSeriesList(): ColorSeries[] {\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{ name: \"随机色\", color: \"#FF6B35\" },\r\n\t\t\t\t\t{ name: \"黑白灰\", color: \"#808080\" },\r\n\t\t\t\t\t{ name: \"红色\", color: \"#FF4444\" },\r\n\t\t\t\t\t{ name: \"橙色\", color: \"#FF8844\" },\r\n\t\t\t\t\t{ name: \"黄色\", color: \"#FFDD44\" },\r\n\t\t\t\t\t{ name: \"绿色\", color: \"#44FF44\" },\r\n\t\t\t\t\t{ name: \"青色\", color: \"#44FFFF\" },\r\n\t\t\t\t\t{ name: \"蓝色\", color: \"#4444FF\" },\r\n\t\t\t\t\t{ name: \"紫色\", color: \"#AA44FF\" },\r\n\t\t\t\t\t{ name: \"粉色\", color: \"#FF88CC\" },\r\n\t\t\t\t\t{ name: \"棕色\", color: \"#AA6644\" }\r\n\t\t\t\t]\r\n\t\t\t},\r\n\r\n\t\t\t// 根据选中的系列生成120个颜色（10行12列）\r\n\t\t\tcolorList() : string[] {\r\n\t\t\t\tconst colors : string[] = []\r\n\r\n\t\t\t\tfor (let i = 0; i < 120; i++) {\r\n\t\t\t\t\tconst row = Math.floor(i / 12) // 当前行（0-9）\r\n\t\t\t\t\tconst col = i % 12 // 当前列（0-11）\r\n\r\n\t\t\t\t\t// 计算位置因子\r\n\t\t\t\t\tconst rowFactor = row / 9.0 // 行因子 0-1\r\n\t\t\t\t\tconst colFactor = col / 11.0 // 列因子 0-1\r\n\r\n\t\t\t\t\t// 基于选中的系列索引确定颜色系列\r\n\t\t\t\t\tlet r: number, g: number, b: number\r\n\r\n\t\t\t\t\tif (this.selectedSeriesIndex == 0) {\r\n\t\t\t\t\t\t// 随机色系列 - 每个方块完全随机的RGB值\r\n\t\t\t\t\t\tconst seed1 = (row * 12 + col + this.randomSeed) * 0.1\r\n\t\t\t\t\t\tconst seed2 = (row * 12 + col + this.randomSeed + 100) * 0.13\r\n\t\t\t\t\t\tconst seed3 = (row * 12 + col + this.randomSeed + 200) * 0.17\r\n\t\t\t\t\t\tr = Math.round((Math.sin(seed1) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tg = Math.round((Math.sin(seed2) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tb = Math.round((Math.sin(seed3) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 1) {\r\n\t\t\t\t\t\t// 黑白灰系列 - 更细腻的灰度变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1的完整渐变\r\n\t\t\t\t\t\tconst grayValue = Math.round(totalFactor * 255)\r\n\t\t\t\t\t\tr = grayValue\r\n\t\t\t\t\t\tg = grayValue\r\n\t\t\t\t\t\tb = grayValue\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 2) {\r\n\t\t\t\t\t\t// 红色系列 - 更丰富的红色变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\t\t\t\t\t\tconst brightness = 0.2 + totalFactor * 0.8 // 0.2-1.0的亮度范围\r\n\t\t\t\t\t\tconst saturation = 0.3 + (1 - Math.abs(totalFactor - 0.5) * 2) * 0.7 // 中间饱和度高\r\n\t\t\t\t\t\tr = Math.round(brightness * 255)\r\n\t\t\t\t\t\tg = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t\tb = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 其他颜色系列 - 确保包含纯色且避免黑色\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\r\n\t\t\t\t\t\t// 根据系列索引确定基础色相\r\n\t\t\t\t\t\tlet baseHue: number\r\n\t\t\t\t\t\tif (this.selectedSeriesIndex == 3) baseHue = 30      // 橙色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 4) baseHue = 60 // 黄色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 5) baseHue = 120 // 绿色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 6) baseHue = 180 // 青色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 7) baseHue = 240 // 蓝色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 8) baseHue = 300 // 紫色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 9) baseHue = 330 // 粉色\r\n\t\t\t\t\t\telse baseHue = 25 // 棕色\r\n\r\n\t\t\t\t\t\t// 色相微调：在基础色相±10度范围内变化\r\n\t\t\t\t\t\tconst hue = baseHue + (colFactor - 0.5) * 20\r\n\r\n\t\t\t\t\t\t// 创造三种类型的颜色变化\r\n\t\t\t\t\t\tif (totalFactor < 0.4) {\r\n\t\t\t\t\t\t\t// 前40%：深色调 - 高饱和度，中低明度（避免太暗）\r\n\t\t\t\t\t\t\tconst localFactor = totalFactor / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 + localFactor * 0.2 // 0.8-1.0\r\n\t\t\t\t\t\t\tconst value = 0.4 + localFactor * 0.3 // 0.4-0.7（避免太暗）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else if (totalFactor < 0.6) {\r\n\t\t\t\t\t\t\t// 中20%：纯色调 - 最高饱和度，最佳明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.4) / 0.2\r\n\t\t\t\t\t\t\tconst saturation = 1.0 // 最高饱和度\r\n\t\t\t\t\t\t\tconst value = 0.8 + localFactor * 0.2 // 0.8-1.0（确保亮度足够）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 后40%：浅色调 - 降低饱和度，保持高明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.6) / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 - localFactor * 0.6 // 0.8-0.2（逐渐降低饱和度）\r\n\t\t\t\t\t\t\tconst value = 0.9 + localFactor * 0.1 // 0.9-1.0（保持高明度）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 确保RGB值在0-255范围内\r\n\t\t\t\t\tr = Math.max(0, Math.min(255, r))\r\n\t\t\t\t\tg = Math.max(0, Math.min(255, g))\r\n\t\t\t\t\tb = Math.max(0, Math.min(255, b))\r\n\r\n\t\t\t\t\tcolors.push(`rgb(${r}, ${g}, ${b})`)\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn colors\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 最终的RGBA颜色值\r\n\t\t\tfinalColor() : string {\r\n\t\t\t\t// 优先使用自定义颜色\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\t// 提取RGB值并添加透明度\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tconst r = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tconst g = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tconst b = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t\treturn `rgba(${r}, ${g}, ${b}, ${this.opacity})`\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn `rgba(255, 0, 0, ${this.opacity})`\r\n\t\t\t},\r\n\r\n\t\t\t// 临时RGB颜色预览\r\n\t\t\ttempRGBColor() : string {\r\n\t\t\t\treturn `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 颜色系列选择事件\r\n\t\t\tonSeriesSelect(index: number) {\r\n\t\t\t\tthis.selectedSeriesIndex = index\r\n\t\t\t\tthis.selectedColorIndex = 0 // 重置选中的颜色\r\n\t\t\t\tthis.customColor = \"\" // 清除自定义颜色\r\n\r\n\t\t\t\t// 如果选择的是随机色系列，生成新的随机种子\r\n\t\t\t\tif (index == 0) { // 随机色现在是第1个按钮，索引为0\r\n\t\t\t\t\tthis.randomSeed = Math.floor(Math.random() * 1000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 显示透明度选择器\r\n\t\t\tshowOpacityPicker() {\r\n\t\t\t\tconst opacityOptions = [\r\n\t\t\t\t\t'100%', '95%', '90%', '85%', '80%', '75%', '70%', '65%', '60%', '55%',\r\n\t\t\t\t\t'50%', '45%', '40%', '35%', '30%', '25%', '20%', '15%', '10%', '5%'\r\n\t\t\t\t]\r\n\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: opacityOptions,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconst selectedOpacity = (100 - res.tapIndex * 5) / 100\r\n\t\t\t\t\t\tthis.opacity = selectedOpacity\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 颜色选择事件\r\n\t\t\tonColorSelect(index : number) {\r\n\t\t\t\tthis.selectedColorIndex = index\r\n\t\t\t\t// 清除自定义颜色，使用新选中的颜色\r\n\t\t\t\tthis.customColor = \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 显示RGB设置弹窗\r\n\t\t\tshowRGBPicker() {\r\n\t\t\t\t// 获取当前颜色的RGB值（优先使用自定义颜色）\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tthis.tempR = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tthis.tempG = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tthis.tempB = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t}\r\n\t\t\t\tthis.showRGBModal = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭RGB设置弹窗\r\n\t\t\tcloseRGBPicker() {\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// RGB弹窗点击事件（阻止冒泡）\r\n\t\t\tonRGBModalClick() {\r\n\t\t\t\t// 空方法，用于阻止事件冒泡\r\n\t\t\t},\r\n\r\n\t\t\t// 确认RGB设置\r\n\t\t\tconfirmRGBPicker() {\r\n\t\t\t\t// 设置自定义颜色\r\n\t\t\t\tthis.customColor = `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// R值滑块变化\r\n\t\t\tonTempRChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempR = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// G值滑块变化\r\n\t\t\tonTempGChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempG = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// B值滑块变化\r\n\t\t\tonTempBChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempB = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// R值输入框变化\r\n\t\t\tonTempRInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempR = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// G值输入框变化\r\n\t\t\tonTempGInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempG = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// B值输入框变化\r\n\t\t\tonTempBInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempB = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 打开弹窗\r\n\t\t\topen() {\r\n\t\t\t\tthis.visible = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\r\n\t\t\t// 点击遮罩层关闭弹窗\r\n\t\t\tonOverlayClick() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 取消按钮点击事件\r\n\t\t\tonCancel() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 确定按钮点击事件\r\n\t\t\tonConfirm() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tconst rgbaValues = this.getRGBAValues()\r\n\t\t\t\tthis.$emit('confirm', {\r\n\t\t\t\t\tcolor: this.finalColor,\r\n\t\t\t\t\trgba: rgbaValues,\r\n\t\t\t\t\thex: this.rgbToHex(rgbaValues.r, rgbaValues.g, rgbaValues.b)\r\n\t\t\t\t})\r\n\t\t\t},\r\n \r\n\t\t\t// 获取RGBA数值\r\n\t\t\tgetRGBAValues() : RGBAValues {\r\n\t\t\t\tconst rgbaMatch = this.finalColor.match(/rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+),\\s*([\\d.]+)\\)/)\r\n\t\t\t\tif (rgbaMatch != null) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tr: parseInt(rgbaMatch[1] as string),\r\n\t\t\t\t\t\tg: parseInt(rgbaMatch[2] as string),\r\n\t\t\t\t\t\tb: parseInt(rgbaMatch[3] as string),\r\n\t\t\t\t\t\ta: parseFloat(rgbaMatch[4] as string)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn { r: 255, g: 0, b: 0, a: 1.0 }\r\n\t\t\t},\r\n\r\n\t\t\t// HSV转RGB\r\n\t\t\thsvToRgb(h: number, s: number, v: number): RGBValues {\r\n\t\t\t\tconst c: number = v * s\r\n\t\t\t\tconst x: number = c * (1.0 - Math.abs(((h / 60.0) % 2.0) - 1.0))\r\n\t\t\t\tconst m: number = v - c\r\n\r\n\t\t\t\tlet r: number = 0.0\r\n\t\t\t\tlet g: number = 0.0\r\n\t\t\t\tlet b: number = 0.0\r\n\r\n\t\t\t\tif (h >= 0 && h < 60) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 60 && h < 120) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 120 && h < 180) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = x\r\n\t\t\t\t} else if (h >= 180 && h < 240) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 240 && h < 300) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 300 && h < 360) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = x\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst result: RGBValues = {\r\n\t\t\t\t\tr: Math.round((r + m) * 255.0),\r\n\t\t\t\t\tg: Math.round((g + m) * 255.0),\r\n\t\t\t\t\tb: Math.round((b + m) * 255.0)\r\n\t\t\t\t}\r\n\t\t\t\treturn result\r\n\t\t\t},\r\n\r\n\t\t\t// RGB转十六进制\r\n\t\t\trgbToHex(r: number, g: number, b: number): string {\r\n\t\t\t\tconst toHex = (value: number): string => {\r\n\t\t\t\t\tconst hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)\r\n\t\t\t\t\treturn hex.length == 1 ? '0' + hex : hex\r\n\t\t\t\t}\r\n\t\t\t\treturn '#' + toHex(r) + toHex(g) + toHex(b)\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 弹窗遮罩层 */\r\n\t.picker-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.picker-modal {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-top-left-radius: 20rpx;\r\n\t\tborder-top-right-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.color-picker-container {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\theight: 44px;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #007aff;\r\n\t\tpadding: 8px 12px;\r\n\t}\r\n\r\n\t.cancel-btn {\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.confirm-btn-container {\r\n\t\theight: 30px;\r\n\t\tbackground-color: #007aff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.nav-title {\r\n\t\tfont-size: 17px;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t/* 区域标题样式 */\r\n\t.section-title {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t/* 颜色系列选择区域 */\r\n\t.color-series-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-series-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 10rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t}\r\n\r\n\t.series-button {\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 2px solid transparent;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t/* 随机色按钮：两个普通按钮宽度 + 间距 */\r\n\t.random-button {\r\n\t\twidth: 220rpx;\r\n\t}\r\n\r\n\t/* 其他按钮正常宽度 */\r\n\t.normal-button {\r\n\t\twidth: 100rpx;\r\n\t}\r\n\r\n\t.series-button.active {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t.series-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t\ttext-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n\t}\r\n\r\n\t/* 颜色网格区域 */\r\n\t.color-grid-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: flex-start;\r\n\t\tpadding: 15rpx;\r\n\t}\r\n\r\n\t.color-item {\r\n\t\twidth: 55rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 4px;\r\n\t\tborder: 2px solid transparent;\r\n\t\tmargin-bottom: 4px;\r\n\t\tflex-shrink: 0;\r\n\t\tflex-grow: 0;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.color-item.selected {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t/* 预览和透明度选择区域 */\r\n\t.preview-opacity-section {\r\n\t\tpadding: 15rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.preview-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.rgba-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.opacity-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.opacity-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.opacity-button {\r\n\t\tbackground-color: #007aff;\r\n\t\tpadding: 12rpx 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.opacity-value {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* RGB设置弹窗样式 */\r\n\t.rgb-modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.rgb-modal {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 12rpx;\r\n\t\twidth: 600rpx;\r\n\t\tmax-height: 800rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.rgb-preview-section {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 25rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.rgb-preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.rgb-preview-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t}\r\n\r\n\t.rgb-controls {\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.rgb-control-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-label {\r\n\t\twidth: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.rgb-slider {\r\n\t\tflex: 1;\r\n\t\tmargin: 0 15rpx;\r\n\t}\r\n\r\n\t.rgb-input {\r\n\t\twidth: 120rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tborder-radius: 6rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.rgb-button {\r\n\t\twidth: 45%;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.rgb-cancel {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t}\r\n\r\n\t.rgb-confirm {\r\n\t\tbackground-color: #007aff;\r\n\t}\r\n\r\n\t.rgb-button-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.rgb-cancel .rgb-button-text {\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.rgb-confirm .rgb-button-text {\r\n\t\tcolor: #ffffff;\r\n\t}\r\n</style>", "<template>\r\n\t<!-- 弹窗遮罩层 -->\r\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\r\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\r\n\t\t\t<view class=\"color-picker-container\">\r\n\t\t\t\t<!-- 导航栏 -->\r\n\t\t\t\t<view class=\"navbar\">\r\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\r\n\t\t\t\t\t<text class=\"nav-title\">颜色选择</text>\r\n\t\t\t\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色系列选择按钮 -->\r\n\t\t\t\t<view class=\"color-series-section\">\r\n\t\t\t\t\t<view class=\"color-series-buttons\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(series, index) in colorSeriesList\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"series-button\"\r\n\t\t\t\t\t\t\t:class=\"{\r\n\t\t\t\t\t\t\t\t'active': selectedSeriesIndex == index,\r\n\t\t\t\t\t\t\t\t'random-button': index == 0,\r\n\t\t\t\t\t\t\t\t'normal-button': index != 0\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t:style=\"{ backgroundColor: series.color }\"\r\n\t\t\t\t\t\t\t@click=\"onSeriesSelect(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"series-text\">{{ series.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色方块列表 -->\r\n\t\t\t\t<view class=\"color-grid-section\">\r\n\t\t\t\t\t<view class=\"color-grid\">\r\n\t\t\t\t\t\t<view v-for=\"(color, index) in colorList\" :key=\"index\" class=\"color-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'selected': selectedColorIndex == index }\" :style=\"{ backgroundColor: color }\"\r\n\t\t\t\t\t\t\t@click=\"onColorSelect(index)\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 预览和透明度选择区域 -->\r\n\t\t\t\t<view class=\"preview-opacity-section\">\r\n\t\t\t\t\t<view class=\"preview-area\" @click=\"showRGBPicker\">\r\n\t\t\t\t\t\t<view class=\"preview-color\" :style=\"{ backgroundColor: finalColor }\"></view>\r\n\t\t\t\t\t\t<text class=\"rgba-text\">{{ finalColor }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"opacity-area\">\r\n\t\t\t\t\t\t<text class=\"opacity-label\">透明度</text>\r\n\t\t\t\t\t\t<view class=\"opacity-button\" @click=\"showOpacityPicker\">\r\n\t\t\t\t\t\t\t<text class=\"opacity-value\">{{ Math.round(opacity * 100) }}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- RGB设置弹窗 -->\r\n\t\t\t\t<view v-if=\"showRGBModal\" class=\"rgb-modal-overlay\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t<view class=\"rgb-modal\" @click=\"onRGBModalClick\">\r\n\t\t\t\t\t\t<view class=\"rgb-modal-header\">\r\n\t\t\t\t\t\t\t<text class=\"rgb-modal-title\">RGB颜色设置</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-preview-section\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-preview-color\" :style=\"{ backgroundColor: tempRGBColor }\"></view>\r\n\t\t\t\t\t\t\t<text class=\"rgb-preview-text\">{{ tempRGBColor }}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-controls\">\r\n\t\t\t\t\t\t\t<!-- R值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">R</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempR\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempRChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempR.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempRInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- G值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">G</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempG\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempGChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempG.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempGInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- B值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">B</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempB\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempBChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempB.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempBInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-modal-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-cancel\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">取消</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-confirm\" @click=\"confirmRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">确定</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 定义颜色类型\r\n\ttype ColorInfo = {\r\n\t\tr : number,\r\n\t\tg : number,\r\n\t\tb : number\r\n\t}\r\n\ttype RGBAValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number,\r\n\t  a: number\r\n\t}\r\n\ttype RGBValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number\r\n\t}\r\n\ttype ColorSeries = {\r\n\t  name: string,\r\n\t  color: string\r\n\t}\r\n\texport default {\r\n\t\tname: \"main-color-picker\",\r\n\t\temits: ['cancel', 'confirm'],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 控制弹窗显示\r\n\t\t\t\tvisible: false as boolean,\r\n\t\t\t\t// 当前选中的颜色系列索引\r\n\t\t\t\tselectedSeriesIndex: 0 as number,\r\n\t\t\t\t// 透明度，范围0-1\r\n\t\t\t\topacity: 1.0 as number,\r\n\t\t\t\t// 当前选中的颜色索引\r\n\t\t\t\tselectedColorIndex: 0 as number,\r\n\t\t\t\t// 基础颜色（可以根据需要修改）\r\n\t\t\t\tbaseColor: { r: 255.0, g: 0.0, b: 0.0 } as ColorInfo,\r\n\t\t\t\t// 随机色种子，用于重新生成随机色\r\n\t\t\t\trandomSeed: 0 as number,\r\n\t\t\t\t// RGB设置弹窗相关\r\n\t\t\t\tshowRGBModal: false as boolean,\r\n\t\t\t\ttempR: 255 as number,\r\n\t\t\t\ttempG: 0 as number,\r\n\t\t\t\ttempB: 0 as number,\r\n\t\t\t\t// 自定义颜色\r\n\t\t\t\tcustomColor: \"\" as string\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 颜色系列列表\r\n\t\t\tcolorSeriesList(): ColorSeries[] {\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{ name: \"随机色\", color: \"#FF6B35\" },\r\n\t\t\t\t\t{ name: \"黑白灰\", color: \"#808080\" },\r\n\t\t\t\t\t{ name: \"红色\", color: \"#FF4444\" },\r\n\t\t\t\t\t{ name: \"橙色\", color: \"#FF8844\" },\r\n\t\t\t\t\t{ name: \"黄色\", color: \"#FFDD44\" },\r\n\t\t\t\t\t{ name: \"绿色\", color: \"#44FF44\" },\r\n\t\t\t\t\t{ name: \"青色\", color: \"#44FFFF\" },\r\n\t\t\t\t\t{ name: \"蓝色\", color: \"#4444FF\" },\r\n\t\t\t\t\t{ name: \"紫色\", color: \"#AA44FF\" },\r\n\t\t\t\t\t{ name: \"粉色\", color: \"#FF88CC\" },\r\n\t\t\t\t\t{ name: \"棕色\", color: \"#AA6644\" }\r\n\t\t\t\t]\r\n\t\t\t},\r\n\r\n\t\t\t// 根据选中的系列生成120个颜色（10行12列）\r\n\t\t\tcolorList() : string[] {\r\n\t\t\t\tconst colors : string[] = []\r\n\r\n\t\t\t\tfor (let i = 0; i < 120; i++) {\r\n\t\t\t\t\tconst row = Math.floor(i / 12) // 当前行（0-9）\r\n\t\t\t\t\tconst col = i % 12 // 当前列（0-11）\r\n\r\n\t\t\t\t\t// 计算位置因子\r\n\t\t\t\t\tconst rowFactor = row / 9.0 // 行因子 0-1\r\n\t\t\t\t\tconst colFactor = col / 11.0 // 列因子 0-1\r\n\r\n\t\t\t\t\t// 基于选中的系列索引确定颜色系列\r\n\t\t\t\t\tlet r: number, g: number, b: number\r\n\r\n\t\t\t\t\tif (this.selectedSeriesIndex == 0) {\r\n\t\t\t\t\t\t// 随机色系列 - 每个方块完全随机的RGB值\r\n\t\t\t\t\t\tconst seed1 = (row * 12 + col + this.randomSeed) * 0.1\r\n\t\t\t\t\t\tconst seed2 = (row * 12 + col + this.randomSeed + 100) * 0.13\r\n\t\t\t\t\t\tconst seed3 = (row * 12 + col + this.randomSeed + 200) * 0.17\r\n\t\t\t\t\t\tr = Math.round((Math.sin(seed1) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tg = Math.round((Math.sin(seed2) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tb = Math.round((Math.sin(seed3) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 1) {\r\n\t\t\t\t\t\t// 黑白灰系列 - 更细腻的灰度变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1的完整渐变\r\n\t\t\t\t\t\tconst grayValue = Math.round(totalFactor * 255)\r\n\t\t\t\t\t\tr = grayValue\r\n\t\t\t\t\t\tg = grayValue\r\n\t\t\t\t\t\tb = grayValue\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 2) {\r\n\t\t\t\t\t\t// 红色系列 - 更丰富的红色变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\t\t\t\t\t\tconst brightness = 0.2 + totalFactor * 0.8 // 0.2-1.0的亮度范围\r\n\t\t\t\t\t\tconst saturation = 0.3 + (1 - Math.abs(totalFactor - 0.5) * 2) * 0.7 // 中间饱和度高\r\n\t\t\t\t\t\tr = Math.round(brightness * 255)\r\n\t\t\t\t\t\tg = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t\tb = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 其他颜色系列 - 确保包含纯色且避免黑色\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\r\n\t\t\t\t\t\t// 根据系列索引确定基础色相\r\n\t\t\t\t\t\tlet baseHue: number\r\n\t\t\t\t\t\tif (this.selectedSeriesIndex == 3) baseHue = 30      // 橙色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 4) baseHue = 60 // 黄色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 5) baseHue = 120 // 绿色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 6) baseHue = 180 // 青色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 7) baseHue = 240 // 蓝色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 8) baseHue = 300 // 紫色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 9) baseHue = 330 // 粉色\r\n\t\t\t\t\t\telse baseHue = 25 // 棕色\r\n\r\n\t\t\t\t\t\t// 色相微调：在基础色相±10度范围内变化\r\n\t\t\t\t\t\tconst hue = baseHue + (colFactor - 0.5) * 20\r\n\r\n\t\t\t\t\t\t// 创造三种类型的颜色变化\r\n\t\t\t\t\t\tif (totalFactor < 0.4) {\r\n\t\t\t\t\t\t\t// 前40%：深色调 - 高饱和度，中低明度（避免太暗）\r\n\t\t\t\t\t\t\tconst localFactor = totalFactor / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 + localFactor * 0.2 // 0.8-1.0\r\n\t\t\t\t\t\t\tconst value = 0.4 + localFactor * 0.3 // 0.4-0.7（避免太暗）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else if (totalFactor < 0.6) {\r\n\t\t\t\t\t\t\t// 中20%：纯色调 - 最高饱和度，最佳明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.4) / 0.2\r\n\t\t\t\t\t\t\tconst saturation = 1.0 // 最高饱和度\r\n\t\t\t\t\t\t\tconst value = 0.8 + localFactor * 0.2 // 0.8-1.0（确保亮度足够）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 后40%：浅色调 - 降低饱和度，保持高明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.6) / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 - localFactor * 0.6 // 0.8-0.2（逐渐降低饱和度）\r\n\t\t\t\t\t\t\tconst value = 0.9 + localFactor * 0.1 // 0.9-1.0（保持高明度）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 确保RGB值在0-255范围内\r\n\t\t\t\t\tr = Math.max(0, Math.min(255, r))\r\n\t\t\t\t\tg = Math.max(0, Math.min(255, g))\r\n\t\t\t\t\tb = Math.max(0, Math.min(255, b))\r\n\r\n\t\t\t\t\tcolors.push(`rgb(${r}, ${g}, ${b})`)\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn colors\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 最终的RGBA颜色值\r\n\t\t\tfinalColor() : string {\r\n\t\t\t\t// 优先使用自定义颜色\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\t// 提取RGB值并添加透明度\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tconst r = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tconst g = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tconst b = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t\treturn `rgba(${r}, ${g}, ${b}, ${this.opacity})`\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn `rgba(255, 0, 0, ${this.opacity})`\r\n\t\t\t},\r\n\r\n\t\t\t// 临时RGB颜色预览\r\n\t\t\ttempRGBColor() : string {\r\n\t\t\t\treturn `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 颜色系列选择事件\r\n\t\t\tonSeriesSelect(index: number) {\r\n\t\t\t\tthis.selectedSeriesIndex = index\r\n\t\t\t\tthis.selectedColorIndex = 0 // 重置选中的颜色\r\n\t\t\t\tthis.customColor = \"\" // 清除自定义颜色\r\n\r\n\t\t\t\t// 如果选择的是随机色系列，生成新的随机种子\r\n\t\t\t\tif (index == 0) { // 随机色现在是第1个按钮，索引为0\r\n\t\t\t\t\tthis.randomSeed = Math.floor(Math.random() * 1000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 显示透明度选择器\r\n\t\t\tshowOpacityPicker() {\r\n\t\t\t\tconst opacityOptions = [\r\n\t\t\t\t\t'100%', '95%', '90%', '85%', '80%', '75%', '70%', '65%', '60%', '55%',\r\n\t\t\t\t\t'50%', '45%', '40%', '35%', '30%', '25%', '20%', '15%', '10%', '5%'\r\n\t\t\t\t]\r\n\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: opacityOptions,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconst selectedOpacity = (100 - res.tapIndex * 5) / 100\r\n\t\t\t\t\t\tthis.opacity = selectedOpacity\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 颜色选择事件\r\n\t\t\tonColorSelect(index : number) {\r\n\t\t\t\tthis.selectedColorIndex = index\r\n\t\t\t\t// 清除自定义颜色，使用新选中的颜色\r\n\t\t\t\tthis.customColor = \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 显示RGB设置弹窗\r\n\t\t\tshowRGBPicker() {\r\n\t\t\t\t// 获取当前颜色的RGB值（优先使用自定义颜色）\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tthis.tempR = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tthis.tempG = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tthis.tempB = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t}\r\n\t\t\t\tthis.showRGBModal = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭RGB设置弹窗\r\n\t\t\tcloseRGBPicker() {\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// RGB弹窗点击事件（阻止冒泡）\r\n\t\t\tonRGBModalClick() {\r\n\t\t\t\t// 空方法，用于阻止事件冒泡\r\n\t\t\t},\r\n\r\n\t\t\t// 确认RGB设置\r\n\t\t\tconfirmRGBPicker() {\r\n\t\t\t\t// 设置自定义颜色\r\n\t\t\t\tthis.customColor = `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// R值滑块变化\r\n\t\t\tonTempRChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempR = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// G值滑块变化\r\n\t\t\tonTempGChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempG = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// B值滑块变化\r\n\t\t\tonTempBChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempB = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// R值输入框变化\r\n\t\t\tonTempRInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempR = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// G值输入框变化\r\n\t\t\tonTempGInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempG = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// B值输入框变化\r\n\t\t\tonTempBInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempB = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 打开弹窗\r\n\t\t\topen() {\r\n\t\t\t\tthis.visible = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\r\n\t\t\t// 点击遮罩层关闭弹窗\r\n\t\t\tonOverlayClick() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 取消按钮点击事件\r\n\t\t\tonCancel() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 确定按钮点击事件\r\n\t\t\tonConfirm() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tconst rgbaValues = this.getRGBAValues()\r\n\t\t\t\tthis.$emit('confirm', {\r\n\t\t\t\t\tcolor: this.finalColor,\r\n\t\t\t\t\trgba: rgbaValues,\r\n\t\t\t\t\thex: this.rgbToHex(rgbaValues.r, rgbaValues.g, rgbaValues.b)\r\n\t\t\t\t})\r\n\t\t\t},\r\n \r\n\t\t\t// 获取RGBA数值\r\n\t\t\tgetRGBAValues() : RGBAValues {\r\n\t\t\t\tconst rgbaMatch = this.finalColor.match(/rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+),\\s*([\\d.]+)\\)/)\r\n\t\t\t\tif (rgbaMatch != null) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tr: parseInt(rgbaMatch[1] as string),\r\n\t\t\t\t\t\tg: parseInt(rgbaMatch[2] as string),\r\n\t\t\t\t\t\tb: parseInt(rgbaMatch[3] as string),\r\n\t\t\t\t\t\ta: parseFloat(rgbaMatch[4] as string)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn { r: 255, g: 0, b: 0, a: 1.0 }\r\n\t\t\t},\r\n\r\n\t\t\t// HSV转RGB\r\n\t\t\thsvToRgb(h: number, s: number, v: number): RGBValues {\r\n\t\t\t\tconst c: number = v * s\r\n\t\t\t\tconst x: number = c * (1.0 - Math.abs(((h / 60.0) % 2.0) - 1.0))\r\n\t\t\t\tconst m: number = v - c\r\n\r\n\t\t\t\tlet r: number = 0.0\r\n\t\t\t\tlet g: number = 0.0\r\n\t\t\t\tlet b: number = 0.0\r\n\r\n\t\t\t\tif (h >= 0 && h < 60) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 60 && h < 120) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 120 && h < 180) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = x\r\n\t\t\t\t} else if (h >= 180 && h < 240) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 240 && h < 300) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 300 && h < 360) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = x\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst result: RGBValues = {\r\n\t\t\t\t\tr: Math.round((r + m) * 255.0),\r\n\t\t\t\t\tg: Math.round((g + m) * 255.0),\r\n\t\t\t\t\tb: Math.round((b + m) * 255.0)\r\n\t\t\t\t}\r\n\t\t\t\treturn result\r\n\t\t\t},\r\n\r\n\t\t\t// RGB转十六进制\r\n\t\t\trgbToHex(r: number, g: number, b: number): string {\r\n\t\t\t\tconst toHex = (value: number): string => {\r\n\t\t\t\t\tconst hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)\r\n\t\t\t\t\treturn hex.length == 1 ? '0' + hex : hex\r\n\t\t\t\t}\r\n\t\t\t\treturn '#' + toHex(r) + toHex(g) + toHex(b)\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 弹窗遮罩层 */\r\n\t.picker-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.picker-modal {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-top-left-radius: 20rpx;\r\n\t\tborder-top-right-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.color-picker-container {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\theight: 44px;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #007aff;\r\n\t\tpadding: 8px 12px;\r\n\t}\r\n\r\n\t.cancel-btn {\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.confirm-btn-container {\r\n\t\theight: 30px;\r\n\t\tbackground-color: #007aff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.nav-title {\r\n\t\tfont-size: 17px;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t/* 区域标题样式 */\r\n\t.section-title {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t/* 颜色系列选择区域 */\r\n\t.color-series-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-series-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 10rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t}\r\n\r\n\t.series-button {\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 2px solid transparent;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t/* 随机色按钮：两个普通按钮宽度 + 间距 */\r\n\t.random-button {\r\n\t\twidth: 220rpx;\r\n\t}\r\n\r\n\t/* 其他按钮正常宽度 */\r\n\t.normal-button {\r\n\t\twidth: 100rpx;\r\n\t}\r\n\r\n\t.series-button.active {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t.series-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t\ttext-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n\t}\r\n\r\n\t/* 颜色网格区域 */\r\n\t.color-grid-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: flex-start;\r\n\t\tpadding: 15rpx;\r\n\t}\r\n\r\n\t.color-item {\r\n\t\twidth: 55rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 4px;\r\n\t\tborder: 2px solid transparent;\r\n\t\tmargin-bottom: 4px;\r\n\t\tflex-shrink: 0;\r\n\t\tflex-grow: 0;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.color-item.selected {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t/* 预览和透明度选择区域 */\r\n\t.preview-opacity-section {\r\n\t\tpadding: 15rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.preview-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.rgba-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.opacity-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.opacity-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.opacity-button {\r\n\t\tbackground-color: #007aff;\r\n\t\tpadding: 12rpx 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.opacity-value {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* RGB设置弹窗样式 */\r\n\t.rgb-modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.rgb-modal {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 12rpx;\r\n\t\twidth: 600rpx;\r\n\t\tmax-height: 800rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.rgb-preview-section {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 25rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.rgb-preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.rgb-preview-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t}\r\n\r\n\t.rgb-controls {\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.rgb-control-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-label {\r\n\t\twidth: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.rgb-slider {\r\n\t\tflex: 1;\r\n\t\tmargin: 0 15rpx;\r\n\t}\r\n\r\n\t.rgb-input {\r\n\t\twidth: 120rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tborder-radius: 6rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.rgb-button {\r\n\t\twidth: 45%;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.rgb-cancel {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t}\r\n\r\n\t.rgb-confirm {\r\n\t\tbackground-color: #007aff;\r\n\t}\r\n\r\n\t.rgb-button-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.rgb-cancel .rgb-button-text {\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.rgb-confirm .rgb-button-text {\r\n\t\tcolor: #ffffff;\r\n\t}\r\n</style>", "<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"select-container\" @click=\"showSelector\">\n\t\t\t\t<view class=\"select-text-wrapper\">\n\t\t\t\t\t<text class=\"select-text\" :class=\"{'select-placeholder': selectedText===''}\">\n\t\t\t\t\t\t{{ displayText }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"select-icon-wrapper\">\n\t\t\t\t\t<text class=\"select-icon\">▼</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\ttype SelectOption = {\n\t\ttext: string;\n\t\tvalue: any;\n\t}\n\n\texport default {\n\t\tname: \"FormSelect\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: null as any | null, \n\t\t\t\tisSave: false as boolean,\n\t\t\t\tsave_key: \"\" as string,\n\t\t\t\ttip: \"\" as string,\n\t\t\t\tvarType: \"string\" as string,\n\t\t\t\tselectOptions: [] as SelectOption[],\n\t\t\t\tselectedText: \"\" as string,\n\t\t\t\tplaceholder: \"请选择1\" as string,\n\t\t\t\tshowError: false as boolean,\n\t\t\t\terrorMessage: \"\" as string\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tdisplayText(): string {\n\t\t\t\tif (this.selectedText != \"\") {\n\t\t\t\t\treturn this.selectedText\n\t\t\t\t} else {\n\t\t\t\t\treturn this.placeholder\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tdata: { \n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\tconst newValue = obj.value\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateSelectedText()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name as string\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tconst configVarType = extalJson.getString(\"varType\") ?? \"string\"\n\t\t\t\t// 校验 varType 的有效性\n\t\t\t\tthis.varType = this.validateVarType(configVarType)\n\t\t\t\tthis.placeholder = extalJson.getString(\"placeholder\") ?? \"请选择\"\n\t\t\t\t\n\t\t\t\t// 解析选项数据\n\t\t\t\tconst optionsArray = extalJson.getArray(\"options\")\n\t\t\t\tif (optionsArray != null) {\n\t\t\t\t\tthis.selectOptions = []\n\t\t\t\t\tfor (let i = 0; i < optionsArray.length; i++) {\n\t\t\t\t\t\tconst optionObj = optionsArray[i] as UTSJSONObject\n\t\t\t\t\t\tconst option: SelectOption = {\n\t\t\t\t\t\t\ttext: optionObj.getString(\"text\") ?? \"\",\n\t\t\t\t\t\t\tvalue: optionObj.get(\"value\") ?? \"\"\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.selectOptions.push(option)\n\t\t\t\t\t} \n\t\t\t\t}\n\t\t\t\t// 更新选中文本\n\t\t\t\tthis.updateSelectedText()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 校验 varType 的有效性\n\t\t\tvalidateVarType(varType: string): string {\n\t\t\t\tconst validTypes = [\"string\", \"int\", \"float\"]\n\t\t\t\tif (validTypes.includes(varType)) {\n\t\t\t\t\treturn varType\n\t\t\t\t} else {\n\t\t\t\t\treturn \"string\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 更新选中文本显示\n\t\t\tupdateSelectedText(): void {\n\t\t\t\tif (this.fieldValue != null) {\n\t\t\t\t\t// 查找对应的选项文本，使用宽松比较来处理类型差异\n\t\t\t\t\tconst selectedOption = this.selectOptions.find((option: SelectOption): boolean => {\n\t\t\t\t\t\t// 使用宽松相等比较，处理数字和字符串的类型差异\n\t\t\t\t\t\treturn option.value == this.fieldValue\n\t\t\t\t\t})\n\n\t\t\t\t\tif (selectedOption != null) {\n\t\t\t\t\t\tthis.selectedText = selectedOption.text\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.selectedText = \"\"\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedText = \"\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst cacheData = res.data as string\n\t\t\t\t\t\t\tlet save_value: any\n\n\t\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\t\tif (that.varType == \"int\") {\n\t\t\t\t\t\t\t\tsave_value = parseInt(cacheData)\n\t\t\t\t\t\t\t\t// 验证转换结果\n\t\t\t\t\t\t\t\tif (isNaN(save_value)) {\n\t\t\t\t\t\t\t\t\treturn // 转换失败，不使用缓存\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else if (that.varType == \"float\") {\n\t\t\t\t\t\t\t\tsave_value = parseFloat(cacheData)\n\t\t\t\t\t\t\t\t// 验证转换结果\n\t\t\t\t\t\t\t\tif (isNaN(save_value)) {\n\t\t\t\t\t\t\t\t\treturn // 转换失败，不使用缓存\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// varType == \"string\"\n\t\t\t\t\t\t\t\tsave_value = cacheData\n\t\t\t\t\t\t\t} \n\n\t\t\t\t\t\t\tthat.fieldValue = save_value\n\t\t\t\t\t\t\tthat.updateSelectedText()\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\tvalue: save_value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave && this.fieldValue != null) {\n\t\t\t\t\t// 统一以字符串形式存储\n\t\t\t\t\tconst cacheValue = this.fieldValue.toString()\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: cacheValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 选择器验证\n\t\t\t\tif (this.fieldValue == null) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择一个选项\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value\n\t\t\t\t// 更新显示文本\n\t\t\t\tthis.updateSelectedText()\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 显示选择器\n\t\t\tshowSelector(): void {\n\t\t\t\tif (this.selectOptions.length == 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '暂无选项',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst itemList = this.selectOptions.map((option: SelectOption): string => {\n\t\t\t\t\treturn option.text\n\t\t\t\t})\n\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: itemList,\n\t\t\t\t\tsuccess: (res: ShowActionSheetSuccess) => {\n\t\t\t\t\t\tconst selectedIndex = res.tapIndex\n\t\t\t\t\t\tconst selectedOption = this.selectOptions[selectedIndex] as SelectOption\n\n\t\t\t\t\t\tlet selectedValue = selectedOption.value\n\n\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\tif (this.varType == \"int\") {\n\t\t\t\t\t\t\tif (typeof selectedValue === \"number\") {\n\t\t\t\t\t\t\t\tselectedValue = Math.floor(selectedValue)\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tselectedValue = parseInt(selectedValue.toString())\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (this.varType == \"float\") {\n\t\t\t\t\t\t\tif (typeof selectedValue === \"number\") {\n\t\t\t\t\t\t\t\tselectedValue = selectedValue\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tselectedValue = parseFloat(selectedValue.toString())\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// varType == \"string\"\n\t\t\t\t\t\t\tselectedValue = selectedValue.toString()\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\tvalue: selectedValue\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tconsole.log('选择取消')\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.select-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmin-height: 60rpx;\n\t\tpadding: 10rpx 20rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.select-text-wrapper {\n\t\tflex: 1;\n\t}\n\n\t.select-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.select-placeholder {\n\t\tcolor: #999999;\n\t}\n\n\t.select-icon-wrapper {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.select-icon {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n</style>", "// 农历信息类型\nexport type LunarInfoType = {\n\tlYear: number;\n\tlMonth: number;\n\tlDay: number;\n\tIMonthCn: string;\n\tIDayCn: string;\n\tcYear: number;\n\tcMonth: number;\n\tcDay: number;\n\tgzYear?: string;\n\tgzMonth?: string;\n\tgzDay?: string;\n\tisToday: boolean;\n\tisLeap: boolean;\n\tnWeek?: number;\n\tncWeek?: string;\n\tisTerm?: boolean;\n\tTerm?: string;\n\tastro?: string\n}\n\n// 日期类型\nexport type DateType = {\n\tfullDate: string;\n\tyear: number;\n\tmonth: number;\n\tdate: number;\n\tday: number;\n\tdisabled: boolean;\n\tlunar: string;\n\tis_today: boolean;\n\tdata?: LunarInfoType\n}\n\n// 农历信息辅助类型\nexport type InfoType = {\n\tlunarY: number;\n\tlunarM: number;\n\tlunarD: number;\n\tisLeap: boolean;\n}\n\n/**\n * 农历1900-2100的润大小信息表\n * @Array Of Property\n * @return Hex\n */\nconst lunarYears = [\n\t0x04bd8,\n\t// 1901-2000\n\t0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2, 0x04ae0,\n\t0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977, 0x04970,\n\t0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970, 0x06566,\n\t0x0d4a0, 0x0ea50, 0x16a95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950, 0x0d4a0,\n\t0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557, 0x06ca0,\n\t0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0, 0x0aea6,\n\t0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0, 0x096d0,\n\t0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6, 0x095b0,\n\t0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570, 0x04af5,\n\t0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x05ac0, 0x0ab60, 0x096d5, 0x092e0, 0x0c960,\n\t// 2001-2100\n\t0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5, 0x0a950,\n\t0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930, 0x07954,\n\t0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530, 0x05aa0,\n\t0x076a3, 0x096d0, 0x04afb, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45, 0x0b5a0,\n\t0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0, 0x14b63,\n\t0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0, 0x092e0,\n\t0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4, 0x052d0,\n\t0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0, 0x0b273,\n\t0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160, 0x0e968,\n\t0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252, 0x0d520\n]\n\n// ['月','正','一','二','三','四','五','六','七','八','九','十','冬','腊'];\nconst N_STR_3 = [\"\\u6708\", \"\\u6b63\", \"\\u4e8c\", \"\\u4e09\", \"\\u56db\", \"\\u4e94\", \"\\u516d\", \"\\u4e03\", \"\\u516b\", \"\\u4e5d\", \"\\u5341\", \"\\u51ac\", \"\\u814a\"]\n// ['日','一','二','三','四','五','六','七','八','九','十']\nconst N_STR_1 = [\"\\u65e5\", \"\\u4e00\", \"\\u4e8c\", \"\\u4e09\", \"\\u56db\", \"\\u4e94\", \"\\u516d\", \"\\u4e03\", \"\\u516b\", \"\\u4e5d\", \"\\u5341\"]\n// ['初','十','廿','卅','闰']\nconst N_STR_2 = [\"\\u521d\", \"\\u5341\", \"\\u5eff\", \"\\u5345\", \"\\u95f0\"]\n\n/**\n * 阳历节日\n */\nconst festival = {\n\t'1-1': { title: '元旦节' },\n\t'2-14': { title: '情人节' },\n\t'3-8': { title: '妇女节' },\n\t'3-12': { title: '植树节' },\n\t'4-1': { title: '愚人节' },\n\t'4-4': { title: '清明节' },\n\t'5-1': { title: '劳动节' },\n\t'5-4': { title: '青年节' },\n\t'5-12': { title: '护士节' },\n\t'6-1': { title: '儿童节' },\n\t'7-1': { title: '建党节' },\n\t'8-1': { title: '建军节' },\n\t'9-10': { title: '教师节' },\n\t'10-1': { title: '国庆节' },\n\t'12-24': { title: '平安夜' },\n\t'12-25': { title: '圣诞节' }\n}\n\n/**\n * 农历节日\n */\nconst lfestival = {\n\t'12-30': { title: '除夕' },\n\t'1-1': { title: '春节' },\n\t'1-15': { title: '元宵节' },\n\t'2-2': { title: '龙抬头' },\n\t'5-5': { title: '端午节' },\n\t'7-7': { title: '七夕节' },\n\t'7-15': { title: '中元节' },\n\t'8-15': { title: '中秋节' },\n\t'9-9': { title: '重阳节' },\n\t'10-1': { title: '寒衣节' },\n\t'10-15': { title: '下元节' },\n\t'12-8': { title: '腊八节' },\n\t'12-23': { title: '北方小年' },\n\t'12-24': { title: '南方小年' }\n}\n\n/**\n * 24节气速查表\n */\nconst solarTerm = [\"\\u5c0f\\u5bd2\", \"\\u5927\\u5bd2\", \"\\u7acb\\u6625\", \"\\u96e8\\u6c34\", \"\\u60ca\\u86f0\", \"\\u6625\\u5206\",\n\t\"\\u6e05\\u660e\", \"\\u8c37\\u96e8\", \"\\u7acb\\u590f\", \"\\u5c0f\\u6ee1\", \"\\u8292\\u79cd\", \"\\u590f\\u81f3\",\n\t\"\\u5c0f\\u6691\", \"\\u5927\\u6691\", \"\\u7acb\\u79cb\", \"\\u5904\\u6691\", \"\\u767d\\u9732\", \"\\u79cb\\u5206\",\n\t\"\\u5bd2\\u9732\", \"\\u971c\\u964d\", \"\\u7acb\\u51ac\", \"\\u5c0f\\u96ea\", \"\\u5927\\u96ea\", \"\\u51ac\\u81f3\"]\n\n/**\n * 1900-2100各年的24节气日期速查表\n */\nconst sTermInfo = ['9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\n\t'97bcf97c3598082c95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f',\n\t'b027097bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',\n\t'97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa', '9778397bd19801ec9210c965cc920e',\n\t'97b6b97bd19801ec95f8c965cc920f', '97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2',\n\t'9778397bd197c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e', '97bd09801d98082c95f8e1cfcc920f',\n\t'97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e',\n\t'97bcf97c3598082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f',\n\t'97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\n\t'97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',\n\t'97bd097bd07f595b0b6fc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '9778397bd19801ec9210c9274c920e',\n\t'97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',\n\t'9778397bd097c36c9210c9274c920e', '97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722',\n\t'7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e',\n\t'97bd07f1487f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722',\n\t'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\n\t'97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722',\n\t'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c91aa', '97b6b97bd197c36c9210c9274c920e',\n\t'97bcf7f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2',\n\t'9778397bd097c36c9210c9274c920e', '97b6b7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722',\n\t'7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b70c9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',\n\t'7f0e37f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\n\t'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',\n\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722',\n\t'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c91aa', '97b6b7f0e47f149b0723b0787b0721',\n\t'7f0e27f0e47f531b0723b0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2',\n\t'977837f0e37f149b0723b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722',\n\t'7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0721',\n\t'7f0e37f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\n\t'977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\n\t'7f0e397bd097c35b0b6fc920fb0722', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\n\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\n\t'977837f0e37f14998082b0787b06bd', '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722',\n\t'7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0723b06bd', '7f07e7f0e37f149b0723b0787b0721',\n\t'7f0e27f0e47f531b0723b0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14898082b0723b02d5',\n\t'7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f595b0b0bb0b6fb0722',\n\t'7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722',\n\t'7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35',\n\t'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\n\t'7f0e37f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\n\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f149b0723b0787b0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35',\n\t'7ec967f0e37f14998082b0723b06bd', '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',\n\t'7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0723b06bd', '7f07e7f0e37f14998083b0787b0721',\n\t'7f0e27f0e47f531b0723b0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14898082b0723b02d5',\n\t'7f07e7f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66aa89801e9808297c35',\n\t'665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722',\n\t'7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b072297c35',\n\t'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e26665b66a449801e9808297c35',\n\t'665f67f0e37f1489801eb072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\n\t'7f0e27f1487f531b0b0bb0b6fb0722']\n\n// 完整的农历类\nexport class Lunar {\n\tprivate lunarYearDaysMap = new Map<number, number>()\n\tprivate lunarMonthDaysMap = new Map<number, number[]>()\n\n\tconstructor() { }\n\n\t/**\n\t * 传入农历数字月份返回汉语通俗表示法\n\t * @param lunar month\n\t * @return Cn string\n\t * @eg:let cnMonth = calendar.toChinaMonth(12) ;//cnMonth='腊月'\n\t */\n\ttoChinaMonth(m: number, leap: boolean = false): string { // 月 => \\u6708\n\t\treturn leap ? (N_STR_3[4] + N_STR_3[m] + N_STR_3[0]) : (N_STR_3[m] + N_STR_3[0]);\n\t}\n\n\t/**\n\t * 传入农历日期数字返回汉字表示法\n\t * @param lunar day\n\t * @return Cn string\n\t * @eg:let cnDay = calendar.toChinaDay(21) ;//cnMonth='廿一'\n\t */\n\ttoChinaDay(d: number): string { // 日 => \\u65e5\n\t\tlet s: string\n\t\tswitch (d) {\n\t\t\tcase 10:\n\t\t\t\ts = '\\u521d\\u5341';\n\t\t\t\tbreak\n\t\t\tcase 20:\n\t\t\t\ts = '\\u4e8c\\u5341';\n\t\t\t\tbreak\n\t\t\tcase 30:\n\t\t\t\ts = '\\u4e09\\u5341';\n\t\t\t\tbreak\n\t\t\tdefault:\n\t\t\t\ts = N_STR_2[Math.floor(d / 10)]\n\t\t\t\ts += N_STR_1[d % 10]\n\t\t}\n\t\treturn (s)\n\t}\n\n\t/**\n\t * 返回农历y年闰月是哪个月；若y年没有闰月 则返回0\n\t * @param lunar Year\n\t * @return Number (0-12)\n\t * @eg:let leapMonth = calendar.leapMonth(1987) ;//leapMonth=6\n\t */\n\tleapMonth(year: number): number {\n\t\treturn lunarYears[year - 1900] & 0xF;\n\t}\n\n\t/**\n\t * 返回农历y年闰月的天数 若该年没有闰月则返回0\n\t * @param lunar Year\n\t * @return Number (0、29、30)\n\t * @eg:let leapMonthDay = calendar.leapDays(1987) ;//leapMonthDay=29\n\t */\n\tleapDays(year: number): number {\n\t\tif (this.leapMonth(year) > 0) {\n\t\t\treturn (lunarYears[year - 1900] & 0x10000) != 0 ? 30 : 29;\n\t\t}\n\t\treturn 0;\n\t}\n\n\t// 某年份农历各月天数\n\tlunarMonthDays(year: number): number[] {\n\t\tlet monthDays = this.lunarMonthDaysMap.get(year)\n\t\tif (monthDays != null) {\n\t\t\treturn monthDays\n\t\t}\n\n\t\tmonthDays = [];\n\n\t\tlet lunarYear = lunarYears[year - 1900];\n\n\t\tfor (let i = 15; i >= 4; i--) {\n\t\t\tlet monthDay = (lunarYear >> i & 0x1) != 0 ? 30 : 29;\n\t\t\tmonthDays.push(monthDay);\n\t\t}\n\n\t\t// 添加闰月\n\t\tlet leapM = this.leapMonth(year);\n\n\t\tif (leapM > 0) monthDays.splice(leapM, 0, this.leapDays(year));\n\t\tthis.lunarMonthDaysMap.set(year, monthDays)\n\n\t\treturn monthDays;\n\t}\n\n\t// 某年农历天数\n\tlunarYearDays(year: number): number {\n\t\tif (this.lunarYearDaysMap.has(year)) {\n\t\t\treturn this.lunarYearDaysMap.get(year)!\n\t\t}\n\t\tlet num = 0;\n\t\tthis.lunarMonthDays(year).forEach(item => {\n\t\t\tnum += item;\n\t\t});\n\t\tthis.lunarYearDaysMap.set(year, num)\n\t\treturn num;\n\t}\n\n\t/**\n\t * 传入阳历年月日获得详细的公历、农历object信息 <=>JSON\n\t * @param y  solar year\n\t * @param m  solar month\n\t * @param d  solar day\n\t * @return JSON object\n\t * @eg:__f__('log','at utils/calendar.uts:313',calendar.solar2lunar(1987,11,01));\n\t */\n\tsolar2lunar(y: number, m: number, d: number): LunarInfoType { // 参数区间1900.1.31~2100.12.31\n\t\tlet moonDay = this.solar_date(y, m, d);\n\t\tlet lYear = moonDay.lunarY\n\t\tlet lMonth = moonDay.lunarM\n\t\tlet lDay = moonDay.lunarD\n\t\tlet isLeap = moonDay.isLeap\n\n\t\t// 计算农历日期\n\t\tconst IMonthCn = this.toChinaMonth(lMonth, isLeap)\n\n\t\t// 检查阳历节日\n\t\tconst festivalKey = `${m}-${d}`\n\t\tconst solarFestival = festival[festivalKey]\n\n\t\t// 检查农历节日\n\t\tconst lunarFestivalKey = `${lMonth}-${lDay}`\n\t\tconst lunarFestival = lfestival[lunarFestivalKey]\n\n\t\t// 检查24节气\n\t\tconst firstNode = this.getTerm(y, (m * 2 - 1)); // 返回当月「节」为几日开始\n\t\tconst secondNode = this.getTerm(y, (m * 2)); // 返回当月「气」为几日开始\n\t\tlet isTerm = false;\n\t\tlet Term: string | null = null;\n\t\tif (firstNode == d) {\n\t\t\tisTerm = true;\n\t\t\tTerm = solarTerm[m * 2 - 2];\n\t\t}\n\t\tif (secondNode == d) {\n\t\t\tisTerm = true;\n\t\t\tTerm = solarTerm[m * 2 - 1];\n\t\t}\n\n\t\t// 优先级：节气 > 阳历节日 > 农历节日 > 农历日期\n\t\tlet IDayCn: string\n\t\tif (isTerm && Term != null) {\n\t\t\tIDayCn = Term\n\t\t} else if (solarFestival != null) {\n\t\t\tIDayCn = solarFestival.title\n\t\t} else if (lunarFestival != null) {\n\t\t\tIDayCn = lunarFestival.title\n\t\t} else if (lDay == 1) {\n\t\t\tIDayCn = IMonthCn\n\t\t} else {\n\t\t\tIDayCn = this.toChinaDay(lDay)\n\t\t}\n\n\t\t// 是否今天\n\t\tlet isTodayObj = new Date()\n\t\tlet isToday = false\n\t\tif (isTodayObj.getFullYear() == y && isTodayObj.getMonth() + 1 == m && isTodayObj.getDate() == d) {\n\t\t\tisToday = true\n\t\t}\n\n\t\tlet info: LunarInfoType = {\n\t\t\t'lYear': lYear,\n\t\t\t'lMonth': lMonth,\n\t\t\t'lDay': lDay,\n\t\t\t'IMonthCn': IMonthCn,\n\t\t\t'IDayCn': IDayCn,\n\t\t\t'cYear': y,\n\t\t\t'cMonth': m,\n\t\t\t'cDay': d,\n\t\t\t'isToday': isToday,\n\t\t\t'isLeap': isLeap,\n\t\t\t'isTerm': isTerm,\n\t\t\t'Term': Term\n\t\t}\n\t\treturn info\n\t}\n\n\tsolar_date(y: number, m: number, d: number): InfoType { // 参数区间1900.1.31~2100.12.31\n\t\tlet date = new Date(y, m - 1, d);\n\n\t\t// 参照日期 1901-02-19 正月初一\n\t\tlet offset = (Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()) - Date.UTC(1901, 1, 19)) / 86400000;\n\t\tlet temp = 0\n\t\tlet i: number;\n\t\tfor (i = 1901; i < 2101 && offset > 0; i++) {\n\t\t\ttemp = this.lunarYearDays(i);\n\t\t\toffset -= temp;\n\t\t}\n\t\tif (offset < 0) {\n\t\t\toffset += temp;\n\t\t\ti--;\n\t\t}\n\n\t\t// 农历年、月、日\n\t\tlet isLeap: boolean = false\n\t\tlet j: number = 0;\n\t\tlet monthDays = this.lunarMonthDays(i);\n\t\tlet leapM = this.leapMonth(i);\n\n\t\tif (offset > 0) {\n\t\t\tfor (j = 0; j < monthDays.length && offset > 0; j++) {\n\t\t\t\ttemp = monthDays[j];\n\t\t\t\toffset -= temp;\n\t\t\t}\n\t\t\tif (offset == 0) {\n\t\t\t\tj++;\n\t\t\t}\n\t\t\tif (offset < 0) {\n\t\t\t\toffset += temp;\n\t\t\t}\n\t\t} else {\n\t\t\t// 补偿公历1901年2月的农历信息\n\t\t\tif (offset == -23) {\n\t\t\t\tlet info: InfoType = {\n\t\t\t\t\tlunarY: i,\n\t\t\t\t\tlunarM: 12,\n\t\t\t\t\tlunarD: 8,\n\t\t\t\t\tisLeap: false\n\t\t\t\t}\n\t\t\t\tinfo = info\n\t\t\t}\n\t\t}\n\n\t\t// 矫正闰年月\n\t\tif (leapM > 0) {\n\t\t\tif (j == leapM + 1) {\n\t\t\t\tisLeap = true\n\t\t\t}\n\t\t\tif (j >= leapM + 1) {\n\t\t\t\tj--\n\t\t\t}\n\t\t}\n\t\tconst info: InfoType = {\n\t\t\tlunarY: i,\n\t\t\tlunarM: j,\n\t\t\tlunarD: ++offset,\n\t\t\tisLeap: isLeap\n\t\t}\n\n\t\treturn info\n\t}\n\n\t/**\n\t * 传入公历年获得该年第n个节气的公历日期\n\t * @param y公历年(1900-2100)；n二十四节气中的第几个节气(1~24)；从n=1(小寒)算起\n\t * @return day Number\n\t * @eg:var _24 = calendar.getTerm(1987,3) ;//_24=4;意即1987年2月4日立春\n\t */\n\tgetTerm(y: number, n: number): number {\n\t\tif (y < 1900 || y > 2100) {\n\t\t\treturn -1;\n\t\t}\n\t\tif (n < 1 || n > 24) {\n\t\t\treturn -1;\n\t\t}\n\t\tconst _table = sTermInfo[y - 1900];\n\t\tconst _info = [\n\t\t\tparseInt('0x' + _table.substring(0, 5)).toString(),\n\t\t\tparseInt('0x' + _table.substring(5, 10)).toString(),\n\t\t\tparseInt('0x' + _table.substring(10, 15)).toString(),\n\t\t\tparseInt('0x' + _table.substring(15, 20)).toString(),\n\t\t\tparseInt('0x' + _table.substring(20, 25)).toString(),\n\t\t\tparseInt('0x' + _table.substring(25, 30)).toString()\n\t\t];\n\t\tconst _calday = [\n\t\t\t_info[0].substring(0, 1),\n\t\t\t_info[0].substring(1, 3),\n\t\t\t_info[0].substring(3, 4),\n\t\t\t_info[0].substring(4, 6),\n\n\t\t\t_info[1].substring(0, 1),\n\t\t\t_info[1].substring(1, 3),\n\t\t\t_info[1].substring(3, 4),\n\t\t\t_info[1].substring(4, 6),\n\n\t\t\t_info[2].substring(0, 1),\n\t\t\t_info[2].substring(1, 3),\n\t\t\t_info[2].substring(3, 4),\n\t\t\t_info[2].substring(4, 6),\n\n\t\t\t_info[3].substring(0, 1),\n\t\t\t_info[3].substring(1, 3),\n\t\t\t_info[3].substring(3, 4),\n\t\t\t_info[3].substring(4, 6),\n\n\t\t\t_info[4].substring(0, 1),\n\t\t\t_info[4].substring(1, 3),\n\t\t\t_info[4].substring(3, 4),\n\t\t\t_info[4].substring(4, 6),\n\n\t\t\t_info[5].substring(0, 1),\n\t\t\t_info[5].substring(1, 3),\n\t\t\t_info[5].substring(3, 4),\n\t\t\t_info[5].substring(4, 6),\n\t\t];\n\t\treturn parseInt(_calday[n - 1]);\n\t}\n}\n\n// 日历工具类\nexport class Calendar {\n\tprivate lunar: Lunar\n\t\n\tconstructor() {\n\t\tthis.lunar = new Lunar()\n\t}\n\n\tgetDateInfo(time: string = ''): DateType {\n\t\tconst nowDate = this.getDate(time)\n\t\tconst lunar = this.getlunar(nowDate.year, nowDate.month, nowDate.date)\n\t\tconst item: DateType = nowDate\n\t\titem.data = lunar\n\t\treturn item\n\t}\n\n\t/**\n\t * 获取每周数据\n\t * @param {string} dateData\n\t */\n\tgetWeeks(dateData: string = ''): Array<Array<DateType>> {\n\t\tconst dateObj = this.getDate(dateData)\n\t\tconst year = dateObj.year\n\t\tconst month = dateObj.month\n\t\tlet firstDay = new Date(year, month - 1, 0).getDay()\n\t\t// 获取本月天数\n\t\tlet currentDay = new Date(year, month, 0).getDate()\n\t\t// 上个月末尾几天\n\t\tconst lastMonthDays = this._getLastMonthDays(firstDay, dateObj)\n\t\t// 本月天数\n\t\tconst currentMonthDys = this._currentMonthDys(currentDay, dateObj)\n\t\t// 本月剩余天数\n\t\tconst surplus = 42 - (lastMonthDays.length + currentMonthDys.length)\n\t\t// 下个月开始几天\n\t\tconst nextMonthDays = this._getNextMonthDays(surplus, dateObj)\n\n\t\t// 本月所有日期格子合并\n\t\tlet days: Array<DateType> = []\n\t\tfor (let i = 0; i < lastMonthDays.length; i++) {\n\t\t\tconst item = lastMonthDays[i]\n\t\t\tdays.push(item)\n\t\t}\n\t\tfor (let i = 0; i < currentMonthDys.length; i++) {\n\t\t\tconst item = currentMonthDys[i]\n\t\t\tdays.push(item)\n\t\t}\n\t\tfor (let i = 0; i < nextMonthDays.length; i++) {\n\t\t\tconst item = nextMonthDays[i]\n\t\t\tdays.push(item)\n\t\t}\n\t\tlet weeks: Array<Array<DateType>> = []\n\t\t// 拼接数组  上个月开始几天 + 本月天数+ 下个月开始几天\n\t\tfor (let i = 0; i < days.length; i += 7) {\n\t\t\tconst item: Array<DateType> = days.slice(i, i + 7)\n\t\t\tweeks.push(item);\n\t\t}\n\t\treturn weeks\n\t}\n\n\t/**\n\t * 获取上月剩余天数\n\t */\n\t_getLastMonthDays(firstDay: number, full: DateType): Array<DateType> {\n\t\tlet dateArr: Array<DateType> = []\n\t\tfor (let i = firstDay; i > 0; i--) {\n\t\t\tconst month = full.month - 1\n\t\t\tconst beforeDate = new Date(full.year, month, -i + 1).getDate()\n\t\t\tlet nowDate = full.year + '-' + month + '-' + beforeDate\n\n\t\t\tlet item: DateType = this.getDate(nowDate)\n\t\t\titem.disabled = true\n\n\t\t\tdateArr.push(item)\n\t\t}\n\t\treturn dateArr\n\t}\n\n\t/**\n\t * 获取本月天数\n\t */\n\t_currentMonthDys(dateData: number, full: DateType): Array<DateType> {\n\t\tlet dateArr: Array<DateType> = []\n\t\tfor (let i = 1; i <= dateData; i++) {\n\t\t\tlet nowDate = full.year + '-' + full.month + '-' + i\n\t\t\tlet item: DateType = this.getDate(nowDate)\n\t\t\titem.disabled = false\n\n\t\t\tdateArr.push(item)\n\t\t}\n\t\treturn dateArr\n\t}\n\n\t/**\n\t * 获取下月天数\n\t */\n\t_getNextMonthDays(surplus: number, full: DateType): Array<DateType> {\n\t\tlet dateArr: Array<DateType> = []\n\t\tfor (let i = 1; i < surplus + 1; i++) {\n\t\t\tconst month = full.month + 1\n\t\t\tlet nowDate = full.year + '-' + month + '-' + i\n\t\t\tlet item: DateType = this.getDate(nowDate)\n\t\t\titem.disabled = true\n\n\t\t\tdateArr.push(item)\n\t\t}\n\t\treturn dateArr\n\t}\n\n\t/**\n\t * 计算阴历日期显示\n\t */\n\tgetlunar(year: number, month: number, date: number): LunarInfoType {\n\t\treturn this.lunar.solar2lunar(year, month, date)\n\t}\n\n\t/**\n\t * 获取任意时间\n\t */\n\tgetDate(date: string = '', AddDayCount: number = 0, str: string = 'day'): DateType {\n\t\tlet dd: Date = new Date()\n\t\tif (date !== '') {\n\t\t\tconst datePart = date.split(\" \");\n\t\t\tconst dateData = datePart[0].split(\"-\");\n\t\t\tconst year = parseInt(dateData[0])\n\t\t\tconst month = parseInt(dateData[1])\n\t\t\tconst day = parseInt(dateData[2])\n\n\t\t\tdd = new Date(year, month - 1, day)\n\t\t}\n\n\t\tswitch (str) {\n\t\t\tcase 'day':\n\t\t\t\tdd.setDate(dd.getDate() + AddDayCount);\n\t\t\t\tbreak;\n\t\t\tcase 'month':\n\t\t\t\tdd.setMonth(dd.getMonth() + AddDayCount);\n\t\t\t\tbreak;\n\t\t\tcase 'year':\n\t\t\t\tdd.setFullYear(dd.getFullYear() + AddDayCount);\n\t\t\t\tbreak;\n\t\t}\n\n\t\tconst y = dd.getFullYear();\n\t\tconst m = dd.getMonth() + 1;\n\t\tconst d = dd.getDate();\n\n\t\tlet nowDate = y + '-' + m + '-' + d\n\t\tconst lunarData = this.getlunar(y, m, d)\n\n\t\tconst dataObj: DateType = {\n\t\t\tfullDate: nowDate,\n\t\t\tyear: y,\n\t\t\tmonth: m,\n\t\t\tdate: d,\n\t\t\tday: dd.getDay() + 1,\n\t\t\tlunar: lunarData.IDayCn,\n\t\t\tis_today: lunarData.isToday,\n\t\t\tdisabled: false\n\t\t}\n\n\t\treturn dataObj\n\t}\n\n\t/**\n\t * 判断是否为当前月份\n\t */\n\tisCurrentMonth(year: number, month: number): boolean {\n\t\tconst today = new Date();\n\t\treturn year == today.getFullYear() && month == today.getMonth() + 1;\n\t}\n\n\t/**\n\t * 格式化年月显示 (xxxx/xx)\n\t */\n\tformatYearMonth(year: number, month: number): string {\n\t\tconst monthStr = month < 10 ? '0' + month : month.toString()\n\t\treturn `${year}/${monthStr}`\n\t}\n\n\t/**\n\t * 格式化日期为 YYYY-MM-DD\n\t */\n\tformatDate(year: number, month: number, date: number): string {\n\t\tconst monthStr = month < 10 ? '0' + month : month.toString()\n\t\tconst dateStr = date < 10 ? '0' + date : date.toString()\n\t\treturn `${year}-${monthStr}-${dateStr}`\n\t}\n}\n", "import 'D:/Soft/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts';import App from './App.uvue'\r\n\r\nimport { createSSRApp } from 'vue'\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}\nexport function main(app: IApp) {\n    definePageRoutes();\n    defineAppConfig();\n    (createApp()['app'] as VueApp).mount(app, GenUniApp());\n}\n\nexport class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {\n    override name: string = \"QitTools\"\n    override appid: string = \"__UNI__C178CB1\"\n    override versionName: string = \"1.0.0\"\n    override versionCode: string = \"100\"\n    override uniCompilerVersion: string = \"4.75\"\n    \n    constructor() { super() }\n}\n\nimport GenPagesIndexIndexClass from './pages/index/index.uvue'\nimport GenPagesCalendarTestCalendarTestClass from './pages/calendar-test/calendar-test.uvue'\nfunction definePageRoutes() {\n__uniRoutes.push({ path: \"pages/index/index\", component: GenPagesIndexIndexClass, meta: { isQuit: true } as UniPageMeta, style: _uM([[\"navigationBarTitleText\",\"uni-app x\"]]) } as UniPageRoute)\n__uniRoutes.push({ path: \"pages/calendar-test/calendar-test\", component: GenPagesCalendarTestCalendarTestClass, meta: { isQuit: false } as UniPageMeta, style: _uM([[\"navigationBarTitleText\",\"日历\"]]) } as UniPageRoute)\n}\nconst __uniTabBar: Map<string, any | null> | null = null\nconst __uniLaunchPage: Map<string, any | null> = _uM([[\"url\",\"pages/index/index\"],[\"style\",_uM([[\"navigationBarTitleText\",\"uni-app x\"]])]])\nfunction defineAppConfig(){\n  __uniConfig.entryPagePath = '/pages/index/index'\n  __uniConfig.globalStyle = _uM([[\"navigationBarTextStyle\",\"black\"],[\"navigationBarTitleText\",\"uni-app x\"],[\"navigationBarBackgroundColor\",\"#F8F8F8\"],[\"backgroundColor\",\"#F8F8F8\"]])\n  __uniConfig.getTabBarConfig = ():Map<string, any> | null =>  null\n  __uniConfig.tabBar = __uniConfig.getTabBarConfig()\n  __uniConfig.conditionUrl = ''\n  __uniConfig.uniIdRouter = _uM()\n  \n  __uniConfig.ready = true\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;+BAgCuB;+BCJf;+BAVA;;;;;;ADfD,IAAS,kBACd,OAAO,MAAM,EACb,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,IAAI;;IACtE,OAAO,MACJ,KAAK,CAAC,KACN,MAAM,CAAC,WAAQ,cACd,IACE,SAAS,WAAQ,cACjB,MAAM,MAAM,GACX,WAAQ,aAAsB;QAC/B,OAAO,QAAQ,IAAI,CAAC,IAAC,SAAS,WAAQ,aAAsB;YAC1D,IAAI,UAAU,IAAI;gBAAE,OAAO,WAAQ,OAAO,CAAC;;YAC3C,OAAO,iBAAiB,MAAM,MAAM;QACtC;;IACF;MACA,WAAQ,OAAO,CAAC,IAAI;AAE1B;AAEA,IAAM,yBAAiB,GAAG;AAC1B,IAAS,iBACP,MAAM,MAAM,EACZ,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,OAAO,AAAI,WAAQ,IAAC,SAAS,OAAW;QACtC,IAAM,SAAS,uCACb,MAAK,AAAC,UAAO,OAAK,MAAG,OAAK,MAAG,IAC7B,OAAA,OAAO;YACL,QAAQ,IAAI;QACd;;QAEF,IAAM,QAAQ,WAAW,KAAM;YAE7B,OAAO,KAAK,oBACV,OAAM,IAAI,EACV,SAAQ;YAEV,QAAQ,IAAI;QACd;UAAG;QAEH,OAAO,MAAM,CAAC,IAAC,EAAM;YACnB,aAAa;YACb,QAAQ;QACV;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;IACF;;AACF;AE1DO,IAAS,4BAA4B,WAAQ,OAAO,EAAE;IAC3D,IAAM,OAAO,MAAM;IACnB,IAAM,MAAM,MAAM;IAClB,IAAM,IAAI,MAAM;IAChB,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,KAAK;;IACvE,IAAI,YAAY,cAAoB,IAAI;IACxC,4BACE,OAAI,MAAM,CAAI;QACZ;IACF;MACA,IAAC,MAAM,MAAM,CAAK;QAChB,YAAY,8BACV,OAAA;IAEJ;;IAEF,OAAO,WAAQ,OAAO,GACnB,IAAI,CAAC,OAAI,WAAQ,OAAO,EAAK;QAC5B,OAAO,kBAAkB,OAAO,MAAM,IAAI,IAAI,CAAC,IAAC,SAAS,OAAO,CAAI;YAClE,IAAI,UAAU,IAAI,EAAE;gBAClB,OAAO,KAAK;YACd;YACA,aAAa;YACb,OAAO,IAAI;QACb;;IACF;MACC,OAAK,CAAC,OAAI,OAAO,CAAI;QACpB,OAAO,KAAK;IACd;;AACJ;;IAEA;;AD/BC,IAAI,wBAAgB,CAAA;AAEf;;iBACM,wBAAA;YACT,QAAQ,GAAG,CAAC,cAAY;QACzB;;kBACQ,sBAAA;YACP,QAAQ,GAAG,CAAC,YAAU;QACvB;;kBACQ,MAAA;YACP,QAAQ,GAAG,CAAC,YAAU;QACvB;;4BAEqB,MAAA;YACpB,QAAQ,GAAG,CAAC,yBAAuB;YACnC,IAAI,iBAAiB,CAAC,EAAE;gBACvB,+BACC,QAAO,YACP,WAAU;gBAEX,gBAAgB,KAAK,GAAG;gBACxB,WAAW,KAAI;oBACd,gBAAgB,CAAA;gBACjB,GAAG,IAAI;mBACD,IAAI,KAAK,GAAG,KAAK,gBAAgB,IAAI,EAAE;gBAC7C,gBAAgB,KAAK,GAAG;gBACxB;;QAEF;;eAEQ,MAAA;YACP,QAAQ,GAAG,CAAC,YAAU;QACvB;;;;;;;;;;;;;;AACD;;;;;;;;AEnC2B,WAAhB;IACX;kBAAM,MAAM,CAAC;IACb;mBAAO,MAAM,CAAC;IACd;mBAAO,MAAM,CAAC;IACd;oBAAQ,GAAG,CAAC;IACZ,iBAAU,OAAO,SAAC;IAClB,oBAAa,MAAM,SAAC;IACpB;oBAAQ,cAAa;;;;;;;;;yCAPV,4BAAA;;;;;oHACX,cAAA,KACA,eAAA,MACA,eAAA,MACA,gBAAA,OACA,iBAAA,QACA,oBAAA,WACA,gBAAA;;;;;;;;;iBANA,KAAM,MAAM;;gDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,MAAO,MAAM;;iDAAb;;;;;;mCAAA;oBAAA;;;iBACA,MAAO,MAAM;;iDAAb;;;;;;mCAAA;oBAAA;;;iBACA,OAAQ,GAAG;;kDAAX;;;;;;mCAAA;oBAAA;;;iBACA,QAAU,OAAO;;mDAAjB;;;;;;mCAAA;oBAAA;;;iBACA,WAAa,MAAM;;sDAAnB;;;;;;mCAAA;oBAAA;;;iBACA,OAAQ;;kDAAR;;;;;;mCAAA;oBAAA;;;;AAG6B,WAAlB;IACX;oBAAQ,MAAM,CAAC;IACf;oBAAQ,GAAG,CAAA;;;;;;;;;;;;;ACwGM,WAAZ;IACJ;gBAAI,MAAM,CAAA;IACV;gBAAI,MAAM,CAAA;IACV;gBAAI,MAAK,CAAA;;;oCAHO,aAAA,uDAAA,GAAA,EAAA,CAAA;;;;;;qCAAZ,wBAAA;;;;;gHACJ,YAAA,GACA,YAAA,GACA,YAAA;;;;;;;;;iBAFA,GAAI,MAAM;;8CAAV;;;;;;mCAAA;oBAAA;;;iBACA,GAAI,MAAM;;8CAAV;;;;;;mCAAA;oBAAA;;;iBACA,GAAI,MAAK;;8CAAT;;;;;;mCAAA;oBAAA;;;;AAEiB,WAAb;IACH;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAK,CAAA;;;oCAJQ,cAAA,uDAAA,GAAA,EAAA,CAAA;;;AAMD,WAAZ;IACH;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAK,CAAA;;;oCAHO,aAAA,uDAAA,GAAA,EAAA,CAAA;;;AAKE,WAAd;IACH;mBAAM,MAAM,CAAA;IACZ;oBAAO,MAAK,CAAA;;;oCAFK,eAAA,uDAAA,GAAA,EAAA,CAAA;;;;;;uCAAd,0BAAA;;;;;kHACH,eAAA,MACA,gBAAA;;;;;;;;;iBADA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,OAAO,MAAK;;kDAAZ;;;;;;mCAAA;oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClBe,WAAZ;IACJ;gBAAI,MAAM,CAAA;IACV;gBAAI,MAAM,CAAA;IACV;gBAAI,MAAK,CAAA;;;oCAHO,aAAA,qDAAA,GAAA,EAAA,CAAA;;;;;;sCAAZ,yBAAA;;;;;iHACJ,YAAA,GACA,YAAA,GACA,YAAA;;;;;;;;;iBAFA,GAAI,MAAM;;8CAAV;;;;;;mCAAA;oBAAA;;;iBACA,GAAI,MAAM;;8CAAV;;;;;;mCAAA;oBAAA;;;iBACA,GAAI,MAAK;;8CAAT;;;;;;mCAAA;oBAAA;;;;AAEiB,WAAb;IACH;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAK,CAAA;;;oCAJQ,cAAA,qDAAA,GAAA,EAAA,CAAA;;;AAMD,WAAZ;IACH;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAK,CAAA;;;oCAHO,aAAA,qDAAA,GAAA,EAAA,CAAA;;;AAKE,WAAd;IACH;mBAAM,MAAM,CAAA;IACZ;oBAAO,MAAK,CAAA;;;oCAFK,eAAA,qDAAA,GAAA,EAAA,CAAA;;;;;;wCAAd,2BAAA;;;;;mHACH,eAAA,MACA,gBAAA;;;;;;;;;iBADA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,OAAO,MAAK;;kDAAZ;;;;;;mCAAA;oBAAA;;;;;;;;;;;;;;;;;;AChHkB,WAAf;IACJ;mBAAM,MAAM,CAAA;IACZ;oBAAO,GAAG,CAAA;;;oCAFS,gBAAA,oDAAA,EAAA,EAAA,CAAA;;;;;;wCAAf,2BAAA;;;;;mHACJ,eAAA,MACA,gBAAA;;;;;;;;;iBADA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,OAAO,GAAG;;kDAAV;;;;;;mCAAA;oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvB0B,WAAhB;IACX;oBAAO,MAAM,CAAC;IACd;qBAAQ,MAAM,CAAC;IACf;mBAAM,MAAM,CAAC;IACb;uBAAU,MAAM,CAAC;IACjB;qBAAQ,MAAM,CAAC;IACf;oBAAO,MAAM,CAAC;IACd;qBAAQ,MAAM,CAAC;IACf;mBAAM,MAAM,CAAC;IACb,iBAAS,MAAM,SAAC;IAChB,kBAAU,MAAM,SAAC;IACjB,gBAAQ,MAAM,SAAC;IACf;sBAAS,OAAO,SAAC;IACjB;qBAAQ,OAAO,SAAC;IAChB,gBAAQ,MAAM,SAAC;IACf,iBAAS,MAAM,SAAC;IAChB,iBAAS,OAAO,SAAC;IACjB,eAAO,MAAM,SAAC;IACd,gBAAQ,MAAM,SAAA;;;;;;;;;yCAlBH,4BAAA;;;;;oHACX,gBAAA,OACA,iBAAA,QACA,eAAA,MACA,mBAAA,UACA,iBAAA,QACA,gBAAA,OACA,iBAAA,QACA,eAAA,MACA,iBAAA,QACA,kBAAA,SACA,gBAAA,OACA,kBAAA,SACA,iBAAA,QACA,gBAAA,OACA,iBAAA,QACA,iBAAA,QACA,eAAA,MACA,gBAAA;;;;;;;;;iBAjBA,OAAO,MAAM;;kDAAb;;;;;;mCAAA;oBAAA;;;iBACA,QAAQ,MAAM;;mDAAd;;;;;;mCAAA;oBAAA;;;iBACA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,UAAU,MAAM;;qDAAhB;;;;;;mCAAA;oBAAA;;;iBACA,QAAQ,MAAM;;mDAAd;;;;;;mCAAA;oBAAA;;;iBACA,OAAO,MAAM;;kDAAb;;;;;;mCAAA;oBAAA;;;iBACA,QAAQ,MAAM;;mDAAd;;;;;;mCAAA;oBAAA;;;iBACA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,QAAS,MAAM;;mDAAf;;;;;;mCAAA;oBAAA;;;iBACA,SAAU,MAAM;;oDAAhB;;;;;;mCAAA;oBAAA;;;iBACA,OAAQ,MAAM;;kDAAd;;;;;;mCAAA;oBAAA;;;iBACA,SAAS,OAAO;;oDAAhB;;;;;;mCAAA;oBAAA;;;iBACA,QAAQ,OAAO;;mDAAf;;;;;;mCAAA;oBAAA;;;iBACA,OAAQ,MAAM;;kDAAd;;;;;;mCAAA;oBAAA;;;iBACA,QAAS,MAAM;;mDAAf;;;;;;mCAAA;oBAAA;;;iBACA,QAAS,OAAO;;mDAAhB;;;;;;mCAAA;oBAAA;;;iBACA,MAAO,MAAM;;iDAAb;;;;;;mCAAA;oBAAA;;;iBACA,OAAQ,MAAM;;kDAAd;;;;;;mCAAA;oBAAA;;;;AAIsB,WAAX;IACX;uBAAU,MAAM,CAAC;IACjB;mBAAM,MAAM,CAAC;IACb;oBAAO,MAAM,CAAC;IACd;mBAAM,MAAM,CAAC;IACb;kBAAK,MAAM,CAAC;IACZ;uBAAU,OAAO,SAAC;IAClB;oBAAO,MAAM,CAAC;IACd;uBAAU,OAAO,SAAC;IAClB,eAAO,sBAAa;;;;;;;;;oCATT,uBAAA;;;;;+GACX,mBAAA,UACA,eAAA,MACA,gBAAA,OACA,eAAA,MACA,cAAA,KACA,mBAAA,UACA,gBAAA,OACA,mBAAA,UACA,eAAA;;;;;;;;;iBARA,UAAU,MAAM;;qDAAhB;;;;;;mCAAA;oBAAA;;;iBACA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,OAAO,MAAM;;kDAAb;;;;;;mCAAA;oBAAA;;;iBACA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,KAAK,MAAM;;gDAAX;;;;;;mCAAA;oBAAA;;;iBACA,UAAU,OAAO;;qDAAjB;;;;;;mCAAA;oBAAA;;;iBACA,OAAO,MAAM;;kDAAb;;;;;;mCAAA;oBAAA;;;iBACA,UAAU,OAAO;;qDAAjB;;;;;;mCAAA;oBAAA;;;iBACA,MAAO;;iDAAP;;;;;;mCAAA;oBAAA;;;;AAIsB,WAAX;IACX;qBAAQ,MAAM,CAAC;IACf;qBAAQ,MAAM,CAAC;IACf;qBAAQ,MAAM,CAAC;IACf;qBAAQ,OAAO,SAAC;;;;;;;;;oCAJL,uBAAA;;;;;+GACX,iBAAA,QACA,iBAAA,QACA,iBAAA,QACA,iBAAA;;;;;;;;;iBAHA,QAAQ,MAAM;;mDAAd;;;;;;mCAAA;oBAAA;;;iBACA,QAAQ,MAAM;;mDAAd;;;;;;mCAAA;oBAAA;;;iBACA,QAAQ,MAAM;;mDAAd;;;;;;mCAAA;oBAAA;;;iBACA,QAAQ,OAAO;;mDAAf;;;;;;mCAAA;oBAAA;;;;AAQD,IAAM,+BAAa;AAClB,WAAO;AAEP,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAExF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AACxF,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;AAAE,WAAO;CACxF;AAGD,IAAM,UAAU;IAAC;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;CAAS;AAElJ,IAAM,UAAU;IAAC;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;CAAS;AAE9H,IAAM,UAAU;IAAC;IAAU;IAAU;IAAU;IAAU;CAAS;AAKlE,IAAM,0BAAW;IAChB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,cAAS;QAAE,IAAA,QAAO;KAAO;IACzB,cAAS;QAAE,IAAA,QAAO;KAAO;CACzB;AAKD,IAAM,2BAAY;IACjB,cAAS;QAAE,IAAA,QAAO;KAAM;IACxB,YAAO;QAAE,IAAA,QAAO;KAAM;IACtB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,YAAO;QAAE,IAAA,QAAO;KAAO;IACvB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,cAAS;QAAE,IAAA,QAAO;KAAO;IACzB,aAAQ;QAAE,IAAA,QAAO;KAAO;IACxB,cAAS;QAAE,IAAA,QAAO;KAAQ;IAC1B,cAAS;QAAE,IAAA,QAAO;KAAQ;CAC1B;AAKD,IAAM,YAAY;IAAC;IAAgB;IAAgB;IAAgB;IAAgB;IAAgB;IAClG;IAAgB;IAAgB;IAAgB;IAAgB;IAAgB;IAChF;IAAgB;IAAgB;IAAgB;IAAgB;IAAgB;IAChF;IAAgB;IAAgB;IAAgB;IAAgB;IAAgB;CAAe;AAKhG,IAAM,YAAY;IAAC;IAAkC;IACpD;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;IAAkC;IAAkC;IACpE;CAAiC;AAG5B,WAAO;;;;IACZ,YAAQ,mBAAmB,AAAI,IAAI,MAAM,EAAE,MAAM,GAAG;IACpD,YAAQ,oBAAoB,AAAI,IAAI,MAAM,WAAE,MAAM,IAAK;IAEvD,aAAA,CAAgB;IAQhB,SAAA,aAAa,GAAG,MAAM,EAAE,MAAM,OAAO,GAAG,KAAK,GAAG,MAAM,CAAA;QACrD,OAAO,IAAA;YAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;;YAAI,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;;IAChF;IAQA,SAAA,WAAW,GAAG,MAAM,GAAG,MAAM,CAAA;QAC5B,IAAI,GAAG,MAAM;QACb,MAAQ;AACF,YAAL,EAAO;gBACN,IAAI;AAEA,YAAL,EAAO;gBACN,IAAI;AAEA,YAAL,EAAO;gBACN,IAAI;YAEL;;oBACC,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE,EAAE;oBAC/B,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;;;QAEtB,OAAQ;IACT;IAQA,SAAA,UAAU,MAAM,MAAM,GAAG,MAAM,CAAA;QAC9B,OAAO,UAAU,CAAC,OAAO,IAAI,CAAC,KAAG,GAAG;IACrC;IAQA,SAAA,SAAS,MAAM,MAAM,GAAG,MAAM,CAAA;QAC7B,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC7B,OAAO,IAAA,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,KAAG,OAAO,KAAK,CAAC;AAAG,kBAAE;;AAAG,kBAAE;;;QAE1D,OAAO,CAAC;IACT;IAGA,SAAA,eAAe,MAAM,MAAM,YAAG,MAAM,EAAE;QACrC,IAAI,YAAY,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;QAC3C,IAAI,aAAa,IAAI,EAAE;YACtB,OAAO;;QAGR,YAAY,KAAE;QAEd,IAAI,YAAY,UAAU,CAAC,OAAO,IAAI,CAAC;YAEvC;YAAK,IAAI,YAAI,EAAE;YAAf,MAAiB,KAAK,CAAC;gBACtB,IAAI,WAAW,IAAA,CAAC,cAAa,MAAI,GAAG,KAAK,CAAC;AAAG,sBAAE;;AAAG,sBAAE;;gBACpD,UAAU,IAAI,CAAC;gBAFS;;;QAMzB,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC;QAE3B,IAAI,QAAQ,CAAC;YAAE,UAAU,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC;;QACxD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM;QAEjC,OAAO;IACR;IAGA,SAAA,cAAc,MAAM,MAAM,GAAG,MAAM,CAAA;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO;YACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;;QAElC,IAAI,cAAM,CAAC;QACX,IAAI,CAAC,cAAc,CAAC,MAAM,OAAO,CAAC,IAAA,KAAO;YACxC,OAAO;QACR;;QACA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM;QAChC,OAAO;IACR;IAUA,SAAA,YAAY,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,cAAa;QAC1D,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG;QACpC,IAAI,QAAQ,QAAQ,MAAM;QAC1B,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,OAAO,QAAQ,MAAM;QACzB,IAAI,SAAS,QAAQ,MAAM;QAG3B,IAAM,WAAW,IAAI,CAAC,YAAY,CAAC,QAAQ;QAG3C,IAAM,cAAc,KAAG,IAAC,MAAI;QAC5B,IAAM,gBAAgB,QAAQ,CAAC,YAAY;QAG3C,IAAM,mBAAmB,KAAG,SAAM,MAAI;QACtC,IAAM,gBAAgB,SAAS,CAAC,iBAAiB;QAGjD,IAAM,YAAY,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QAC5C,IAAM,aAAa,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;QACzC,IAAI,SAAS,KAAK;QAClB,IAAI,MAAM,MAAM,IAAU,IAAI;QAC9B,IAAI,aAAa,GAAG;YACnB,SAAS,IAAI;YACb,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;QAE5B,IAAI,cAAc,GAAG;YACpB,SAAS,IAAI;YACb,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;QAI5B,IAAI,QAAQ,MAAM;QAClB,IAAI,UAAU,QAAQ,IAAI,EAAE;YAC3B,SAAS;eACH,IAAI,iBAAiB,IAAI,EAAE;YACjC,SAAS,cAAc,KAAK;eACtB,IAAI,iBAAiB,IAAI,EAAE;YACjC,SAAS,cAAc,KAAK;eACtB,IAAI,QAAQ,CAAC,EAAE;YACrB,SAAS;eACH;YACN,SAAS,IAAI,CAAC,UAAU,CAAC;;QAI1B,IAAI,aAAa,AAAI;QACrB,IAAI,UAAU,KAAK;QACnB,IAAI,WAAW,WAAW,MAAM,KAAK,WAAW,QAAQ,KAAK,CAAC,IAAI,KAAK,WAAW,OAAO,MAAM,GAAG;YACjG,UAAU,IAAI;;QAGf,IAAI,6BACM,gBACC,eACF,iBACI,mBACF,gBACD,YACC,UACF,aACG,kBACD,iBACA,eACF;QAET,OAAO;IACR;IAEA,SAAA,WAAW,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,SAAQ;QACpD,IAAI,OAAO,AAAI,KAAK,GAAG,IAAI,CAAC,EAAE;QAG9B,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,KAAK,WAAW,IAAI,KAAK,QAAQ,IAAI,KAAK,OAAO,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,QAAQ;QAC/G,IAAI,eAAO,CAAC;QACZ,IAAI,GAAG,MAAM;YACb;YAAK,IAAI,IAAI;YAAb,MAAe,IAAI,IAAI,IAAI,SAAS,CAAC;gBACpC,OAAO,IAAI,CAAC,aAAa,CAAC;gBAC1B,UAAU;gBAF4B;;;QAIvC,IAAI,SAAS,CAAC,EAAE;YACf,UAAU;YACV;;QAID,IAAI,QAAQ,OAAO,GAAG,KAAK;QAC3B,IAAI,GAAG,MAAM,GAAG,CAAC;QACjB,IAAI,YAAY,IAAI,CAAC,cAAc,CAAC;QACpC,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC;QAE3B,IAAI,SAAS,CAAC,EAAE;gBACf;gBAAK,IAAI,CAAC;gBAAV,MAAY,IAAI,UAAU,MAAM,IAAI,SAAS,CAAC;oBAC7C,OAAO,SAAS,CAAC,EAAE;oBACnB,UAAU;oBAFqC;;;YAIhD,IAAI,UAAU,CAAC,EAAE;gBAChB;;YAED,IAAI,SAAS,CAAC,EAAE;gBACf,UAAU;;eAEL;YAEN,IAAI,UAAU,CAAC,EAAE,EAAE,EAQlB;;QAIF,IAAI,QAAQ,CAAC,EAAE;YACd,IAAI,KAAK,QAAQ,CAAC,EAAE;gBACnB,SAAS,IAAI;;YAEd,IAAI,KAAK,QAAQ,CAAC,EAAE;gBACnB;;;QAGF,IAAM,gBACL,SAAQ,GACR,SAAQ,GACR,SAAQ,EAAE,QACV,SAAQ;QAGT,OAAO;IACR;IAQA,SAAA,QAAQ,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,MAAM,CAAA;QACpC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;YACzB,OAAO,CAAC,CAAC;;QAEV,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE;YACpB,OAAO,CAAC,CAAC;;QAEV,IAAM,SAAS,SAAS,CAAC,IAAI,IAAI,CAAC;QAClC,IAAM,QAAQ;YACb,SAAS,OAAO,OAAO,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAA,EAAA;YAChD,SAAS,OAAO,OAAO,SAAS,CAAC,CAAC,EAAE,EAAE,GAAG,QAAQ,CAAA,EAAA;YACjD,SAAS,OAAO,OAAO,SAAS,CAAC,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAA,EAAA;YAClD,SAAS,OAAO,OAAO,SAAS,CAAC,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAA,EAAA;YAClD,SAAS,OAAO,OAAO,SAAS,CAAC,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAA,EAAA;YAClD,SAAS,OAAO,OAAO,SAAS,CAAC,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAA,EAAA;SAClD;QACD,IAAM,UAAU;YACf,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YAEvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YAEvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YAEvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YAEvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YAEvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;SACvB;QACD,OAAO,SAAS,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B;;AAIK,WAAO;;;;IACZ,YAAQ,OAAO,KAAK;IAEpB,aAAA;QACC,IAAI,CAAC,KAAK,GAAG,AAAI;IAClB;IAEA,SAAA,YAAY,MAAM,MAAM,GAAG,EAAE,GAAG,SAAQ;QACvC,IAAM,UAAU,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,IAAI;QACrE,IAAM,MAAM,WAAW;QACvB,KAAK,IAAI,GAAG;QACZ,OAAO;IACR;IAMA,SAAA,SAAS,UAAU,MAAM,GAAG,EAAE,GAAG,SAAM,SAAM,WAAU;QACtD,IAAM,UAAU,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,OAAO,QAAQ,IAAI;QACzB,IAAM,QAAQ,QAAQ,KAAK;QAC3B,IAAI,WAAW,AAAI,KAAK,MAAM,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAM;QAElD,IAAI,aAAa,AAAI,KAAK,MAAM,OAAO,CAAC,EAAE,OAAO;QAEjD,IAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,UAAU;QAEvD,IAAM,kBAAkB,IAAI,CAAC,gBAAgB,CAAC,YAAY;QAE1D,IAAM,UAAU,EAAE,GAAG,CAAC,cAAc,MAAM,GAAG,gBAAgB,MAAM;QAEnE,IAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,SAAS;QAGtD,IAAI,MAAM,SAAM,YAAY,KAAE;YAC9B;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,IAAI,cAAc,MAAM;gBACvC,IAAM,OAAO,aAAa,CAAC,EAAE;gBAC7B,KAAK,IAAI,CAAC;gBAF+B;;;YAI1C;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,IAAI,gBAAgB,MAAM;gBACzC,IAAM,OAAO,eAAe,CAAC,EAAE;gBAC/B,KAAK,IAAI,CAAC;gBAFiC;;;YAI5C;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,IAAI,cAAc,MAAM;gBACvC,IAAM,OAAO,aAAa,CAAC,EAAE;gBAC7B,KAAK,IAAI,CAAC;gBAF+B;;;QAI1C,IAAI,OAAO,SAAM,SAAM,aAAa,KAAE;YAEtC;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,IAAI,KAAK,MAAM;gBAC9B,IAAM,MAAM,SAAM,YAAY,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC;gBACjD,MAAM,IAAI,CAAC;gBAFqB,KAAK,CAAC;;;QAIvC,OAAO;IACR;IAKA,SAAA,kBAAkB,UAAU,MAAM,EAAE,MAAM,QAAQ,GAAG,SAAM,UAAS;QACnE,IAAI,SAAS,SAAM,YAAY,KAAE;YACjC;YAAK,IAAI,IAAI;YAAb,MAAuB,IAAI,CAAC;gBAC3B,IAAM,QAAQ,KAAK,KAAK,GAAG,CAAC;gBAC5B,IAAM,aAAa,AAAI,KAAK,KAAK,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO;gBAC7D,IAAI,UAAU,KAAK,IAAI,GAAG,MAAM,QAAQ,MAAM;gBAE9C,IAAI,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC;gBAClC,KAAK,QAAQ,GAAG,IAAI;gBAEpB,QAAQ,IAAI,CAAC;gBARgB;;;QAU9B,OAAO;IACR;IAKA,SAAA,iBAAiB,UAAU,MAAM,EAAE,MAAM,QAAQ,GAAG,SAAM,UAAS;QAClE,IAAI,SAAS,SAAM,YAAY,KAAE;YACjC;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,KAAK;gBACpB,IAAI,UAAU,KAAK,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,MAAM;gBACnD,IAAI,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC;gBAClC,KAAK,QAAQ,GAAG,KAAK;gBAErB,QAAQ,IAAI,CAAC;gBALiB;;;QAO/B,OAAO;IACR;IAKA,SAAA,kBAAkB,SAAS,MAAM,EAAE,MAAM,QAAQ,GAAG,SAAM,UAAS;QAClE,IAAI,SAAS,SAAM,YAAY,KAAE;YACjC;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,IAAI,UAAU,CAAC;gBAC9B,IAAM,QAAQ,KAAK,KAAK,GAAG,CAAC;gBAC5B,IAAI,UAAU,KAAK,IAAI,GAAG,MAAM,QAAQ,MAAM;gBAC9C,IAAI,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC;gBAClC,KAAK,QAAQ,GAAG,IAAI;gBAEpB,QAAQ,IAAI,CAAC;gBANmB;;;QAQjC,OAAO;IACR;IAKA,SAAA,SAAS,MAAM,MAAM,EAAE,OAAO,MAAM,EAAE,MAAM,MAAM,GAAG,cAAa;QACjE,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,OAAO;IAC5C;IAKA,SAAA,QAAQ,MAAM,MAAM,GAAG,EAAE,EAAE,aAAa,MAAM,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,KAAK,GAAG,SAAQ;QACjF,IAAI,IAAI,OAAO,AAAI;QACnB,IAAI,SAAS,IAAI;YAChB,IAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,IAAM,WAAW,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACnC,IAAM,OAAO,SAAS,QAAQ,CAAC,CAAC,CAAC;YACjC,IAAM,QAAQ,SAAS,QAAQ,CAAC,CAAC,CAAC;YAClC,IAAM,MAAM,SAAS,QAAQ,CAAC,CAAC,CAAC;YAEhC,KAAK,AAAI,KAAK,MAAM,QAAQ,CAAC,EAAE;;QAGhC,MAAQ;YACF;gBACJ,GAAG,OAAO,CAAC,GAAG,OAAO,KAAK;YAEtB;gBACJ,GAAG,QAAQ,CAAC,GAAG,QAAQ,KAAK;YAExB;gBACJ,GAAG,WAAW,CAAC,GAAG,WAAW,KAAK;;QAIpC,IAAM,IAAI,GAAG,WAAW;QACxB,IAAM,IAAI,GAAG,QAAQ,KAAK,CAAC;QAC3B,IAAM,IAAI,GAAG,OAAO;QAEpB,IAAI,UAAU,IAAI,MAAM,IAAI,MAAM;QAClC,IAAM,YAAY,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG;QAEtC,IAAM,mBACL,WAAU,SACV,OAAM,GACN,QAAO,GACP,OAAM,GACN,MAAK,GAAG,MAAM,KAAK,CAAC,EACpB,QAAO,UAAU,MAAM,EACvB,WAAU,UAAU,OAAO,EAC3B,WAAU,KAAK;QAGhB,OAAO;IACR;IAKA,SAAA,eAAe,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,OAAO,CAAA;QACnD,IAAM,QAAQ,AAAI;QAClB,OAAO,QAAQ,MAAM,WAAW,MAAM,SAAS,MAAM,QAAQ,KAAK,CAAC;IACpE;IAKA,SAAA,gBAAgB,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAM,CAAA;QACnD,IAAM,WAAW,IAAA,QAAQ,EAAE;YAAG,MAAM;;YAAQ,MAAM,QAAQ,CAAA,EAAA;;QAC1D,OAAO,KAAG,OAAI,MAAI;IACnB;IAKA,SAAA,WAAW,MAAM,MAAM,EAAE,OAAO,MAAM,EAAE,MAAM,MAAM,GAAG,MAAM,CAAA;QAC5D,IAAM,WAAW,IAAA,QAAQ,EAAE;YAAG,MAAM;;YAAQ,MAAM,QAAQ,CAAA,EAAA;;QAC1D,IAAM,UAAU,IAAA,OAAO,EAAE;YAAG,MAAM;;YAAO,KAAK,QAAQ,CAAA,EAAA;;QACtD,OAAO,KAAG,OAAI,MAAI,WAAQ,MAAI;IAC/B;;;;;;;;;;;;;;;;ACjrBK,IAAU,aAAS,cAAA;IACxB,IAAM,MAAM;IACZ,OAAO,IACN,SAAA;AAEF;AACM,IAAU,KAAK,KAAK,IAAI,EAAA;IAC1B;IACA;IACA,CAAC,WAAW,CAAC,MAAM,CAAA,EAAA,CAAI,MAAM,EAAE,KAAK,CAAC,KAAK;AAC9C;AAEM,WAAO,eAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS;IACjE,aAAS,MAAM,MAAM,GAAG,UAAU;IAClC,aAAS,OAAO,MAAM,GAAG,gBAAgB;IACzC,aAAS,aAAa,MAAM,GAAG,OAAO;IACtC,aAAS,aAAa,MAAM,GAAG,KAAK;IACpC,aAAS,oBAAoB,MAAM,GAAG,MAAM;IAE5C,gBAAgB,KAAK,GAArB,CAAwB;;AAK5B,IAAS,mBAAgB;IACzB,YAAY,IAAI,cAAG,OAAM,qBAAqB,qCAAoC,mBAAQ,SAAQ,IAAI,GAAmB,QAAO,IAAM,4BAAyB;IAC/J,YAAY,IAAI,cAAG,OAAM,qCAAqC,mDAAkD,mBAAQ,SAAQ,KAAK,GAAmB,QAAO,IAAM,4BAAyB;AAC9L;AAEA,IAAM,iBAAiB,IAAI,MAAM,EAAE,GAAG,KAAW,IAAM,SAAM,qBAAsB,WAAQ,IAAM,4BAAyB;AAC1H,IAAS,kBAAe;IACtB,YAAY,aAAa,GAAG;IAC5B,YAAY,WAAW,GAAG,IAAM,4BAAyB,SAAU,4BAAyB,aAAc,kCAA+B,WAAY,qBAAkB;IACvK,YAAY,eAAe,GAAG,OAAG,IAAI,MAAM,EAAE,GAAG;eAAa,IAAI;;IACjE,YAAY,MAAM,GAAG,YAAY,eAAe;IAChD,YAAY,YAAY,GAAG;IAC3B,YAAY,WAAW,GAAG;IAE1B,YAAY,KAAK,GAAG,IAAI;AAC1B;;;;8BA1CA,EAAA;;;;8BAAA,EAAA;;;;uBAAA,EAAA"}