@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.showToast as uni_showToast
open class GenPagesCalendarTestCalendarTest : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {
        onCreated(fun() {
            val today = Date()
            val year = today.getFullYear()
            val month = today.getMonth() + 1
            val date = today.getDate()
            this.initialDate = "" + year + "-" + (if (month < 10) {
                "0" + month
            } else {
                month
            }
            ) + "-" + (if (date < 10) {
                "0" + date
            } else {
                date
            }
            )
        }
        , __ins)
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_main_calendar_picker = resolveComponent("main-calendar-picker")
        return _cE("scroll-view", _uM("class" to "container"), _uA(
            _cE("view", _uM("class" to "content"), _uA(
                _cE("text", _uM("class" to "title"), "日历弹窗测试"),
                _cE("view", _uM("class" to "test-section"), _uA(
                    _cE("button", _uM("class" to "test-btn", "onClick" to _ctx.openCalendarPicker), "打开日历选择器", 8, _uA(
                        "onClick"
                    )),
                    _cE("view", _uM("class" to "result-section"), _uA(
                        _cE("text", _uM("class" to "result-label"), "选择的日期："),
                        _cE("text", _uM("class" to "result-value"), _tD(_ctx.selectedDate), 1)
                    ))
                ))
            )),
            _cV(_component_main_calendar_picker, _uM("ref" to "calendarPicker", "initial-date" to _ctx.initialDate, "onConfirm" to _ctx.onCalendarConfirm, "onCancel" to _ctx.onCalendarCancel), null, 8, _uA(
                "initial-date",
                "onConfirm",
                "onCancel"
            ))
        ))
    }
    open var initialDate: String by `$data`
    open var selectedDate: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("initialDate" to "" as String, "selectedDate" to "" as String)
    }
    open var openCalendarPicker = ::gen_openCalendarPicker_fn
    open fun gen_openCalendarPicker_fn() {
        val calendarPicker = this.`$refs`["calendarPicker"] as ComponentPublicInstance
        calendarPicker.`$callMethod`("open")
    }
    open var onCalendarConfirm = ::gen_onCalendarConfirm_fn
    open fun gen_onCalendarConfirm_fn(dateData: UTSJSONObject) {
        console.log("选择的日期:", dateData, " at pages/calendar-test/calendar-test.uvue:63")
        val date = dateData.getString("date")
        if (date != null) {
            this.selectedDate = date
        }
        uni_showToast(ShowToastOptions(title = "\u5DF2\u9009\u62E9: " + this.selectedDate, icon = "success"))
    }
    open var onCalendarCancel = ::gen_onCalendarCancel_fn
    open fun gen_onCalendarCancel_fn() {
        console.log("取消选择日期", " at pages/calendar-test/calendar-test.uvue:77")
        uni_showToast(ShowToastOptions(title = "取消选择", icon = "none"))
    }
    companion object {
        var name = "calendar-test"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ), _uA(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("container" to _pS(_uM("flex" to 1, "backgroundColor" to "#f5f5f5")), "content" to _pS(_uM("paddingTop" to "40rpx", "paddingRight" to "40rpx", "paddingBottom" to "40rpx", "paddingLeft" to "40rpx", "display" to "flex", "flexDirection" to "column", "alignItems" to "center")), "title" to _pS(_uM("fontSize" to "48rpx", "fontWeight" to "bold", "color" to "#333333", "marginBottom" to "60rpx")), "test-section" to _pS(_uM("width" to "100%", "maxWidth" to "600rpx", "backgroundColor" to "#ffffff", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "paddingTop" to "40rpx", "paddingRight" to "40rpx", "paddingBottom" to "40rpx", "paddingLeft" to "40rpx", "boxShadow" to "0 4rpx 20rpx rgba(0, 0, 0, 0.1)")), "test-btn" to _pS(_uM("width" to "100%", "height" to "80rpx", "backgroundColor" to "#007aff", "color" to "#ffffff", "borderTopLeftRadius" to "12rpx", "borderTopRightRadius" to "12rpx", "borderBottomRightRadius" to "12rpx", "borderBottomLeftRadius" to "12rpx", "fontSize" to "32rpx", "fontWeight" to "bold", "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000", "marginBottom" to "40rpx")), "result-section" to _pS(_uM("display" to "flex", "flexDirection" to "column", "alignItems" to "center", "paddingTop" to "30rpx", "paddingRight" to "30rpx", "paddingBottom" to "30rpx", "paddingLeft" to "30rpx", "backgroundColor" to "#f8f9fa", "borderTopLeftRadius" to "12rpx", "borderTopRightRadius" to "12rpx", "borderBottomRightRadius" to "12rpx", "borderBottomLeftRadius" to "12rpx")), "result-label" to _pS(_uM("fontSize" to "28rpx", "color" to "#666666", "marginBottom" to "10rpx")), "result-value" to _pS(_uM("fontSize" to "36rpx", "color" to "#007aff", "fontWeight" to "bold")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM())
        var propsNeedCastKeys: UTSArray<String> = _uA()
        var components: Map<String, CreateVueComponent> = _uM("MainCalendarPicker" to GenComponentsMainCalendarPickerClass)
    }
}
