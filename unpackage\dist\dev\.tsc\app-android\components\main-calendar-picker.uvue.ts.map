{"version": 3, "file": "components/main-calendar-picker.uvue", "names": [], "sources": ["components/main-calendar-picker.uvue"], "sourcesContent": ["<template>\n\t<!-- 弹窗遮罩层 -->\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\n\t\t\t<view class=\"calendar-picker-container\">\n\t\t\t\t<!-- 导航栏 -->\n\t\t\t\t<view class=\"navbar\">\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\n\t\t\t\t\t<text class=\"nav-title\">选择日期</text>\n\t\t\t\t\t<view class=\"confirm-btn-container\">\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 年月控制区域 -->\n\t\t\t\t<view class=\"calendar-header\">\n\t\t\t\t\t<view class=\"month-nav-btn\" @click=\"prevMonth\">\n\t\t\t\t\t\t<text class=\"nav-arrow\">‹</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"year-month-display\" @click=\"openYearMonthPicker\">\n\t\t\t\t\t\t<text class=\"year-month-text\">{{ currentYearMonth }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"month-nav-btn\" @click=\"nextMonth\">\n\t\t\t\t\t\t<text class=\"nav-arrow\">›</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 星期标题 -->\n\t\t\t\t<view class=\"week-header\">\n\t\t\t\t\t<view v-for=\"(day, index) in weekDays\" :key=\"index\" class=\"week-day\">\n\t\t\t\t\t\t<text class=\"week-day-text\">{{ day }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 日历网格 -->\n\t\t\t\t<view class=\"calendar-grid\">\n\t\t\t\t\t<view v-for=\"(week, weekIndex) in weeks\" :key=\"weekIndex\" class=\"calendar-week\">\n\t\t\t\t\t\t<view v-for=\"(day, dayIndex) in week\" :key=\"dayIndex\" \n\t\t\t\t\t\t\tclass=\"calendar-day\"\n\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t'day-disabled': day.disabled,\n\t\t\t\t\t\t\t\t'day-today': day.is_today,\n\t\t\t\t\t\t\t\t'day-selected': isSelectedDay(day)\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t@click=\"onDaySelect(day)\">\n\t\t\t\t\t\t\t<text class=\"day-number\" \n\t\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t\t'day-number-disabled': day.disabled,\n\t\t\t\t\t\t\t\t\t'day-number-today': day.is_today,\n\t\t\t\t\t\t\t\t\t'day-number-selected': isSelectedDay(day)\n\t\t\t\t\t\t\t\t}\">{{ day.date }}</text>\n\t\t\t\t\t\t\t<text class=\"day-lunar\"\n\t\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t\t'day-lunar-disabled': day.disabled,\n\t\t\t\t\t\t\t\t\t'day-lunar-today': day.is_today,\n\t\t\t\t\t\t\t\t\t'day-lunar-selected': isSelectedDay(day)\n\t\t\t\t\t\t\t\t}\">{{ day.lunar }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 当前选择显示 -->\n\t\t\t\t<view class=\"current-selection\">\n\t\t\t\t\t<text class=\"selection-label\">当前选择：</text>\n\t\t\t\t\t<text class=\"selection-value\">{{ selectedDateText }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\n\t<!-- 年月选择器 -->\n\t<main-yearmonth-picker \n\t\tref=\"yearmonthPicker\" \n\t\t:initial-year=\"currentYear\"\n\t\t:initial-month=\"currentMonth\"\n\t\t@confirm=\"onYearMonthConfirm\" \n\t\t@cancel=\"onYearMonthCancel\">\n\t</main-yearmonth-picker>\n</template>\n\n<script>\n\timport { Calendar, DateType } from '@/pages/calendar/index.uts'\n\timport MainYearmonthPicker from './main-form/tools/main-yearmonth-picker.uvue'\n\n\texport default {\n\t\tname: \"main-calendar-picker\",\n\t\tcomponents: {\n\t\t\tMainYearmonthPicker\n\t\t},\n\t\temits: ['cancel', 'confirm'],\n\t\tprops: {\n\t\t\t// 初始日期 (YYYY-MM-DD格式)\n\t\t\tinitialDate: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: () => {\n\t\t\t\t\tconst today = new Date()\n\t\t\t\t\tconst year = today.getFullYear()\n\t\t\t\t\tconst month = today.getMonth() + 1\n\t\t\t\t\tconst date = today.getDate()\n\t\t\t\t\treturn `${year}-${month < 10 ? '0' + month : month}-${date < 10 ? '0' + date : date}`\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 控制弹窗显示\n\t\t\t\tvisible: false as boolean,\n\t\t\t\t// 日历工具类实例\n\t\t\t\tcalendar: new Calendar() as Calendar,\n\t\t\t\t// 当前年份\n\t\t\t\tcurrentYear: new Date().getFullYear() as number,\n\t\t\t\t// 当前月份\n\t\t\t\tcurrentMonth: (new Date().getMonth() + 1) as number,\n\t\t\t\t// 当前选中的日期\n\t\t\t\tselectedDate: \"\" as string,\n\t\t\t\t// 日历周数据\n\t\t\t\tweeks: [] as Array<Array<DateType>>,\n\t\t\t\t// 星期标题\n\t\t\t\tweekDays: ['一', '二', '三', '四', '五', '六', '日'] as string[]\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 当前年月显示文本\n\t\t\tcurrentYearMonth(): string {\n\t\t\t\treturn this.calendar.formatYearMonth(this.currentYear, this.currentMonth)\n\t\t\t},\n\t\t\t// 选中日期显示文本\n\t\t\tselectedDateText(): string {\n\t\t\t\tif (this.selectedDate === \"\") {\n\t\t\t\t\treturn \"未选择\"\n\t\t\t\t}\n\t\t\t\tconst parts = this.selectedDate.split('-')\n\t\t\t\tif (parts.length == 3) {\n\t\t\t\t\treturn `${parts[0]}年${parseInt(parts[1])}月${parseInt(parts[2])}日`\n\t\t\t\t}\n\t\t\t\treturn this.selectedDate\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.initializeData()\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化数据\n\t\t\tinitializeData() {\n\t\t\t\t// 解析初始日期\n\t\t\t\tconst dateParts = this.initialDate.split('-')\n\t\t\t\tif (dateParts.length == 3) {\n\t\t\t\t\tthis.currentYear = parseInt(dateParts[0])\n\t\t\t\t\tthis.currentMonth = parseInt(dateParts[1])\n\t\t\t\t\tthis.selectedDate = this.initialDate\n\t\t\t\t}\n\t\t\t\tthis.updateCalendar()\n\t\t\t},\n\n\t\t\t// 更新日历数据\n\t\t\tupdateCalendar() {\n\t\t\t\tconst dateStr = `${this.currentYear}-${this.currentMonth}-1`\n\t\t\t\tthis.weeks = this.calendar.getWeeks(dateStr)\n\t\t\t},\n\n\t\t\t// 判断是否为选中日期\n\t\t\tisSelectedDay(day: DateType): boolean {\n\t\t\t\tif (this.selectedDate === \"\" || day.disabled) {\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\treturn day.fullDate === this.selectedDate\n\t\t\t},\n\n\t\t\t// 日期选择事件\n\t\t\tonDaySelect(day: DateType) {\n\t\t\t\tif (day.disabled) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.selectedDate = day.fullDate\n\t\t\t},\n\n\t\t\t// 上个月\n\t\t\tprevMonth() {\n\t\t\t\tif (this.currentMonth == 1) {\n\t\t\t\t\tthis.currentYear-- \n\t\t\t\t\tthis.currentMonth = 12\n\t\t\t\t} else {\n\t\t\t\t\tthis.currentMonth--\n\t\t\t\t}\n\t\t\t\tthis.updateCalendar()\n\t\t\t},\n\n\t\t\t// 下个月\n\t\t\tnextMonth() {\n\t\t\t\tif (this.currentMonth == 12) {\n\t\t\t\t\tthis.currentYear++\n\t\t\t\t\tthis.currentMonth = 1\n\t\t\t\t} else {\n\t\t\t\t\tthis.currentMonth++\n\t\t\t\t}\n\t\t\t\tthis.updateCalendar()\n\t\t\t},\n\n\t\t\t// 打开年月选择器\n\t\t\topenYearMonthPicker() {\n\t\t\t\tconst yearmonthPicker = this.$refs[\"yearmonthPicker\"] as ComponentPublicInstance\n\t\t\t\tyearmonthPicker.$callMethod(\"open\")\n\t\t\t},\n\n\t\t\t// 年月选择确认\n\t\t\tonYearMonthConfirm(yearMonthData: UTSJSONObject) {\n\t\t\t\tconst year = yearMonthData.getNumber(\"year\")\n\t\t\t\tconst month = yearMonthData.getNumber(\"month\")\n\t\t\t\t\n\t\t\t\tif (year != null && month != null) {\n\t\t\t\t\tthis.currentYear = year\n\t\t\t\t\tthis.currentMonth = month\n\t\t\t\t\tthis.updateCalendar()\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 年月选择取消\n\t\t\tonYearMonthCancel() {\n\t\t\t\t// 取消选择，不做任何操作\n\t\t\t},\n\n\t\t\t// 打开弹窗\n\t\t\topen() {\n\t\t\t\tthis.visible = true\n\t\t\t},\n\n\t\t\t// 关闭弹窗\n\t\t\tclose() {\n\t\t\t\tthis.visible = false\n\t\t\t},\n\n\t\t\t// 点击遮罩层关闭弹窗\n\t\t\tonOverlayClick() {\n\t\t\t\tthis.close()\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\n\t\t\t// 取消按钮点击事件\n\t\t\tonCancel() {\n\t\t\t\tthis.close()\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\n\t\t\t// 确定按钮点击事件\n\t\t\tonConfirm() {\n\t\t\t\tif (this.selectedDate === \"\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择日期',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.close()\n\t\t\t\tthis.$emit('confirm', {\n\t\t\t\t\tdate: this.selectedDate,\n\t\t\t\t\tyear: this.currentYear,\n\t\t\t\t\tmonth: this.currentMonth,\n\t\t\t\t\tday: parseInt(this.selectedDate.split('-')[2])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* 弹窗遮罩层 */\n\t.picker-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 1000;\n\t}\n\n\t.picker-modal {\n\t\twidth: 90%;\n\t\tmax-width: 700rpx;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n\t}\n\n\t.calendar-picker-container {\n\t\twidth: 100%;\n\t\tbackground-color: #ffffff;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t/* 导航栏样式 */\n\t.navbar {\n\t\theight: 44px;\n\t\tbackground-color: #f8f8f8;\n\t\tborder-bottom: 1px solid #e5e5e5;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 10px;\n\t}\n\n\t.nav-btn {\n\t\tfont-size: 16px;\n\t\tcolor: #007aff;\n\t\tpadding: 8px 12px;\n\t}\n\n\t.cancel-btn {\n\t\tcolor: #999999;\n\t}\n\n\t.confirm-btn-container {\n\t\theight: 30px;\n\t\tbackground-color: #007aff;\n\t\tborder-radius: 8rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\n\t}\n\n\t.confirm-btn {\n\t\tcolor: #ffffff;\n\t\tfont-weight: bold;\n\t}\n\n\t.nav-title {\n\t\tfont-size: 17px;\n\t\tcolor: #333333;\n\t}\n\n\t/* 年月控制区域 */\n\t.calendar-header {\n\t\theight: 80rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-bottom: 1px solid #e5e5e5;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 20rpx;\n\t}\n\n\t.month-nav-btn {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tbackground-color: rgba(0, 122, 255, 0.1);\n\t\tborder-radius: 30rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.nav-arrow {\n\t\tfont-size: 32rpx;\n\t\tcolor: #007aff;\n\t\tfont-weight: bold;\n\t}\n\n\t.year-month-display {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 0 20rpx;\n\t}\n\n\t.year-month-text {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t/* 星期标题 */\n\t.week-header {\n\t\theight: 60rpx;\n\t\tbackground-color: #f0f0f0;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\n\t.week-day {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.week-day-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t\tfont-weight: bold;\n\t}\n\n\t/* 日历网格 */\n\t.calendar-grid {\n\t\tpadding: 10rpx;\n\t\tbackground-color: #ffffff;\n\t}\n\n\t.calendar-week {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.calendar-day {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin: 0 2rpx;\n\t\tborder-radius: 8rpx;\n\t\tposition: relative;\n\t}\n\n\t.calendar-day.day-today {\n\t\tbackground-color: rgba(255, 69, 58, 0.1);\n\t\tborder: 2rpx solid #ff453a;\n\t}\n\n\t.calendar-day.day-selected {\n\t\tbackground-color: #007aff;\n\t\ttransform: scale(1.05);\n\t}\n\n\t.calendar-day.day-disabled {\n\t\topacity: 0.3;\n\t}\n\n\t.day-number {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: bold;\n\t\tline-height: 1;\n\t}\n\n\t.day-number-today {\n\t\tcolor: #ff453a;\n\t}\n\n\t.day-number-selected {\n\t\tcolor: #ffffff;\n\t}\n\n\t.day-number-disabled {\n\t\tcolor: #cccccc;\n\t}\n\n\t.day-lunar {\n\t\tfont-size: 20rpx;\n\t\tcolor: #999999;\n\t\tline-height: 1;\n\t\tmargin-top: 4rpx;\n\t}\n\n\t.day-lunar-today {\n\t\tcolor: #ff453a;\n\t}\n\n\t.day-lunar-selected {\n\t\tcolor: #ffffff;\n\t}\n\n\t.day-lunar-disabled {\n\t\tcolor: #cccccc;\n\t}\n\n\t/* 当前选择显示区域 */\n\t.current-selection {\n\t\tpadding: 20rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-top: 1px solid #e5e5e5;\n\t}\n\n\t.selection-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.selection-value {\n\t\tfont-size: 32rpx;\n\t\tcolor: #007aff;\n\t\tfont-weight: bold;\n\t}\n</style>\n"], "mappings": ";CAiFC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;CAE7E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;KACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;KACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrF;GACD;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACzD;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACzE,CAAC;GACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;KAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;KACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACxB;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;GACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;KAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAC5C,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACzC,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACjB,CAAC,CAAC,CAAC,CAAC,CAAC;IACN;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAChC,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC;GACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;KAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACtB,EAAE,CAAC,CAAC,CAAC,EAAE;KACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC;GACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;KAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACrB,EAAE,CAAC,CAAC,CAAC,EAAE;KACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACnC,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;KAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;KACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;KACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;GACD,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACd,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;GACnB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;GACpB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACpB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACpB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;KAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACZ,CAAC;KACD,CAAC,CAAC,CAAC,CAAC,CAAC;IACN;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;GACF;EACD;CACD;;;;;;;;;WApQY,YAAO;QAAnB,IAkEO;;UAlEc,KAAK,EAAC,gBAAgB;UAAE,OAAK,EAAE,mBAAc;;UACjE,IAgEO;YAhED,KAAK,EAAC,cAAc;YAAE,OAAK,gBAAN,QAAc;;YACxC,IA8DO,cA9DD,KAAK,EAAC,2BAA2B;cAEtC,IAMO,cAND,KAAK,EAAC,QAAQ;gBACnB,IAA4D;kBAAtD,KAAK,EAAC,oBAAoB;kBAAE,OAAK,EAAE,aAAQ;oBAAE,IAAE;gBACrD,IAAmC,cAA7B,KAAK,EAAC,WAAW,KAAC,MAAI;gBAC5B,IAEO,cAFD,KAAK,EAAC,uBAAuB;kBAClC,IAA8D;oBAAxD,KAAK,EAAC,qBAAqB;oBAAE,OAAK,EAAE,cAAS;sBAAE,IAAE;;;cAKzD,IAUO,cAVD,KAAK,EAAC,iBAAiB;gBAC5B,IAEO;kBAFD,KAAK,EAAC,eAAe;kBAAE,OAAK,EAAE,cAAS;;kBAC5C,IAAgC,cAA1B,KAAK,EAAC,WAAW,KAAC,GAAC;;gBAE1B,IAEO;kBAFD,KAAK,EAAC,oBAAoB;kBAAE,OAAK,EAAE,wBAAmB;;kBAC3D,IAA2D,cAArD,KAAK,EAAC,iBAAiB,SAAI,qBAAgB;;gBAElD,IAEO;kBAFD,KAAK,EAAC,eAAe;kBAAE,OAAK,EAAE,cAAS;;kBAC5C,IAAgC,cAA1B,KAAK,EAAC,WAAW,KAAC,GAAC;;;cAK3B,IAIO,cAJD,KAAK,EAAC,aAAa;gBACxB,IAEO,yCAFsB,aAAQ,GAAvB,GAAG,EAAE,KAAK,EAAV,OAAG;yBAAjB,IAEO;oBAFiC,GAAG,EAAE,KAAK;oBAAE,KAAK,EAAC,UAAU;;oBACnE,IAA4C,cAAtC,KAAK,EAAC,eAAe,SAAI,GAAG;;;;cAKpC,IAwBO,cAxBD,KAAK,EAAC,eAAe;gBAC1B,IAsBO,yCAtB2B,UAAK,GAAzB,IAAI,EAAE,SAAS,EAAf,OAAI;yBAAlB,IAsBO;oBAtBmC,GAAG,EAAE,SAAS;oBAAE,KAAK,EAAC,eAAe;;oBAC9E,IAoBO,yCApByB,IAAI,GAAtB,GAAG,EAAE,QAAQ,EAAb,OAAG;6BAAjB,IAoBO;wBApBgC,GAAG,EAAE,QAAQ;wBACnD,KAAK,OAAC,cAAc,EACZ;;;;SAIP;wBACA,OAAK,SAAE,gBAAW,CAAC,GAAG;;wBACvB,IAKyB;0BALnB,KAAK,OAAC,YAAY,EACf;;;;UAIP;gCAAK,GAAG,CAAC,IAAI;wBACf,IAK0B;0BALpB,KAAK,OAAC,WAAW,EACd;;;;UAIP;gCAAK,GAAG,CAAC,KAAK;;;;;;cAMnB,IAGO,cAHD,KAAK,EAAC,mBAAmB;gBAC9B,IAA0C,cAApC,KAAK,EAAC,iBAAiB,KAAC,OAAK;gBACnC,IAA2D,cAArD,KAAK,EAAC,iBAAiB,SAAI,qBAAgB;;;;;;IAOrD,IAMwB;MALvB,GAAG,EAAC,iBAAiB;MACpB,cAAY,EAAE,gBAAW;MACzB,eAAa,EAAE,iBAAY;MAC3B,SAAO,EAAE,uBAAkB;MAC3B,QAAM,EAAE,sBAAiB"}