{"version": 3, "file": "index.uts", "sourceRoot": "", "sources": ["pages/calendar/index.uts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAA;AAErD,MAAM,MAAM,QAAQ,GAAG;IACtB,QAAQ,EAAG,MAAM,CAAC;IAClB,IAAI,EAAG,MAAM,CAAC;IACd,KAAK,EAAG,MAAM,CAAC;IACf,IAAI,EAAG,MAAM,CAAC;IACd,GAAG,EAAG,MAAM,CAAC;IACb,QAAQ,EAAG,OAAO,CAAC;IACnB,KAAK,EAAG,MAAM,CAAC;IACf,QAAQ,EAAG,OAAO,CAAC;IACnB,IAAK,CAAC,EAAE,aAAa,CAAA;CACrB,CAAA;AAED,MAAM,OAAO,QAAQ;IACpB,OAAO,CAAC,KAAK,EAAC,KAAK,CAAA;IACnB;QACC,IAAI,CAAC,KAAK,GAAE,IAAI,KAAK,EAAE,CAAA;IACxB,CAAC;IAED,WAAW,CAAC,IAAI,EAAG,MAAM,GAAG,EAAE,GAAI,QAAQ;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QACtE,MAAM,IAAI,EAAG,QAAQ,GAAG,OAAO,CAAA;QAC/B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAA;QACjB,OAAO,IAAI,CAAA;IACZ,CAAC;IAED;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAG,MAAM,GAAG,EAAE,GAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACtC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACzB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QAC3B,IAAI,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAA;QACpD,SAAS;QACT,IAAI,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA;QACnD,UAAU;QACV,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QAC/D,OAAO;QACP,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;QAClE,SAAS;QACT,MAAM,OAAO,GAAG,EAAE,GAAG,CAAC,aAAa,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAA;QACpE,UAAU;QACV,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAC9D,mBAAmB;QAEnB,aAAa;QACb,IAAI,IAAI,EAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAA;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACf;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,CAAA;YAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACf;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACf;QACD,IAAI,KAAK,EAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAA;QACvC,gCAAgC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACxC,MAAM,IAAI,EAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;YACnD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjB;QACD,OAAO,KAAK,CAAA;IACb,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAG,MAAM,EAAE,IAAI,EAAG,QAAQ,GAAI,KAAK,CAAC,QAAQ,CAAC;QACtE,IAAI,OAAO,EAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAA;QAClC,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YAC5B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA;YAC/D,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,UAAU,CAAA;YAExD,IAAI,IAAI,EAAG,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;YAEpB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAClB;QACD,OAAO,OAAO,CAAA;IACf,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAG,MAAM,EAAE,IAAI,EAAG,QAAQ,GAAI,KAAK,CAAC,QAAQ,CAAC;QAErE,IAAI,OAAO,EAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAA;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,CAAA;YACpD,IAAI,IAAI,EAAG,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC3C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;YAErB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAClB;QACD,OAAO,OAAO,CAAA;IACf,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,OAAO,EAAG,MAAM,EAAE,IAAI,EAAG,QAAQ,GAAI,KAAK,CAAC,QAAQ,CAAC;QACrE,IAAI,OAAO,EAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAA;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAA;YAC/C,IAAI,IAAI,EAAG,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;YAEpB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAClB;QACD,OAAO,OAAO,CAAA;IACf,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAG,MAAM,EAAE,KAAK,EAAG,MAAM,EAAE,IAAI,EAAG,MAAM,GAAI,aAAa;QACrE,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;IACjD,CAAC;IAGD;;OAEG;IACH,OAAO,CAAC,IAAI,EAAG,MAAM,GAAG,EAAE,EAAE,WAAW,EAAG,MAAM,GAAG,CAAC,EAAE,GAAG,EAAG,MAAM,GAAG,KAAK,GAAI,QAAQ;QACrF,IAAI,EAAE,EAAG,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;QAC1B,IAAI,IAAI,KAAK,EAAE,EAAE;YAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAClC,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YACnC,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAEjC,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAA;SACnC;QAED,QAAQ,GAAG,EAAE;YACZ,KAAK,KAAK;gBACT,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,CAAC;gBACvC,MAAM;YACP,KAAK,OAAO;gBACX,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,CAAC;gBACzC,MAAM;YACP,KAAK,MAAM;gBACV,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC,CAAC;gBAC/C,MAAM;SACP;QAED,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3B,MAAM,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;QAEvB,IAAI,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;QAExC,MAAM,OAAO,EAAG,QAAQ,GAAG;YAC1B,QAAQ,EAAE,OAAO;YACjB,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC;YACpB,KAAK,EAAE,SAAS,CAAC,MAAM;YACvB,QAAQ,EAAE,SAAS,CAAC,OAAO;YAC3B,QAAQ,EAAE,KAAK;SACf,CAAA;QAED,OAAO,OAAO,CAAA;IACf,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM;QACnD,MAAM,QAAQ,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,IAAE,CAAA;QAC5D,OAAO,GAAG,IAAI,IAAI,QAAQ,EAAE,CAAA;IAC7B,CAAC;CAED", "sourcesContent": ["import { Lunar, LunarInfoType } from './calendar.uts'\r\n\r\nexport type DateType = {\r\n\tfullDate : string;\r\n\tyear : number;\r\n\tmonth : number;\r\n\tdate : number;\r\n\tday : number;\r\n\tdisabled : boolean;\r\n\tlunar : string;\r\n\tis_today : boolean;\r\n\tdata ?: LunarInfoType\r\n}\r\n\r\nexport class Calendar {\r\n\tprivate lunar:Lunar\r\n\tconstructor() {\r\n\t\tthis.lunar =new Lunar()\r\n\t}\r\n\r\n\tgetDateInfo(time : string = '') : DateType {\r\n\t\tconst nowDate = this.getDate(time)\r\n\t\tconst lunar = this.getlunar(nowDate.year, nowDate.month, nowDate.date)\r\n\t\tconst item : DateType = nowDate\r\n\t\titem.data = lunar\r\n\t\treturn item\r\n\t}\r\n\r\n\t/**\r\n\t * 获取每周数据\r\n\t * @param {Object} dateData\r\n\t */\r\n\tgetWeeks(dateData : string = '') : Array<Array<DateType>> {\r\n\t\tconst dateObj = this.getDate(dateData)\r\n\t\tconst year = dateObj.year\r\n\t\tconst month = dateObj.month\r\n\t\tlet firstDay = new Date(year, month - 1, 0).getDay()\r\n\t\t// 获取本月天数\r\n\t\tlet currentDay = new Date(year, month, 0).getDate() \r\n\t\t// 上个月末尾几天\r\n\t\tconst lastMonthDays = this._getLastMonthDays(firstDay, dateObj)\r\n\t\t// 本月天数\r\n\t\tconst currentMonthDys = this._currentMonthDys(currentDay, dateObj)\r\n\t\t// 本月剩余天数\r\n\t\tconst surplus = 42 - (lastMonthDays.length + currentMonthDys.length)\r\n\t\t// 下个月开始几天\r\n\t\tconst nextMonthDays = this._getNextMonthDays(surplus, dateObj)\r\n\t\t// const weeks = []\r\n\r\n\t\t// 本月所有日期格子合并\r\n\t\tlet days : Array<DateType> = []\r\n\t\tfor (let i = 0; i < lastMonthDays.length; i++) {\r\n\t\t\tconst item = lastMonthDays[i]\r\n\t\t\tdays.push(item)\r\n\t\t}\r\n\t\tfor (let i = 0; i < currentMonthDys.length; i++) {\r\n\t\t\tconst item = currentMonthDys[i]\r\n\t\t\tdays.push(item)\r\n\t\t}\r\n\t\tfor (let i = 0; i < nextMonthDays.length; i++) {\r\n\t\t\tconst item = nextMonthDays[i]\r\n\t\t\tdays.push(item)\r\n\t\t}\r\n\t\tlet weeks : Array<Array<DateType>> = []\r\n\t\t// 拼接数组  上个月开始几天 + 本月天数+ 下个月开始几天\r\n\t\tfor (let i = 0; i < days.length; i += 7) {\r\n\t\t\tconst item : Array<DateType> = days.slice(i, i + 7)\r\n\t\t\tweeks.push(item);\r\n\t\t}\r\n\t\treturn weeks\r\n\t}\r\n\r\n\t/**\r\n\t * 获取上月剩余天数\r\n\t */\r\n\t_getLastMonthDays(firstDay : number, full : DateType) : Array<DateType> {\r\n\t\tlet dateArr : Array<DateType> = []\r\n\t\tfor (let i = firstDay; i > 0; i--) {\r\n\t\t\tconst month = full.month - 1\r\n\t\t\tconst beforeDate = new Date(full.year, month, -i + 1).getDate()\r\n\t\t\tlet nowDate = full.year + '-' + month + '-' + beforeDate\r\n\r\n\t\t\tlet item : DateType = this.getDate(nowDate)\r\n\t\t\titem.disabled = true\r\n\r\n\t\t\tdateArr.push(item)\r\n\t\t}\r\n\t\treturn dateArr\r\n\t}\r\n\r\n\t/**\r\n\t * 获取本月天数\r\n\t */\r\n\t_currentMonthDys(dateData : number, full : DateType) : Array<DateType> {\r\n\r\n\t\tlet dateArr : Array<DateType> = []\r\n\t\tfor (let i = 1; i <= dateData; i++) {\r\n\t\t\tlet nowDate = full.year + '-' + full.month + '-' + i\r\n\t\t\tlet item : DateType = this.getDate(nowDate)\r\n\t\t\titem.disabled = false\r\n\r\n\t\t\tdateArr.push(item)\r\n\t\t}\r\n\t\treturn dateArr\r\n\t}\r\n\r\n\t/**\r\n\t * 获取下月天数\r\n\t */\r\n\t_getNextMonthDays(surplus : number, full : DateType) : Array<DateType> {\r\n\t\tlet dateArr : Array<DateType> = []\r\n\t\tfor (let i = 1; i < surplus + 1; i++) {\r\n\t\t\tconst month = full.month + 1\r\n\t\t\tlet nowDate = full.year + '-' + month + '-' + i\r\n\t\t\tlet item : DateType = this.getDate(nowDate)\r\n\t\t\titem.disabled = true\r\n\r\n\t\t\tdateArr.push(item)\r\n\t\t}\r\n\t\treturn dateArr\r\n\t}\r\n\r\n\t/**\r\n\t * 计算阴历日期显示\r\n\t */\r\n\tgetlunar(year : number, month : number, date : number) : LunarInfoType {\r\n\t\treturn this.lunar.solar2lunar(year, month, date)\r\n\t}\r\n\t\r\n\r\n\t/**\r\n\t * 获取任意时间\r\n\t */\r\n\tgetDate(date : string = '', AddDayCount : number = 0, str : string = 'day') : DateType {\r\n\t\tlet dd : Date = new Date()\r\n\t\tif (date !== '') {\r\n\t\t\tconst datePart = date.split(\" \");\r\n\t\t\tconst dateData = datePart[0].split(\"-\");\r\n\t\t\tconst year = parseInt(dateData[0])\r\n\t\t\tconst month = parseInt(dateData[1])\r\n\t\t\tconst day = parseInt(dateData[2]) \r\n\r\n\t\t\tdd = new Date(year, month - 1, day)\r\n\t\t}\r\n\r\n\t\tswitch (str) {\r\n\t\t\tcase 'day':\r\n\t\t\t\tdd.setDate(dd.getDate() + AddDayCount);\r\n\t\t\t\tbreak;\r\n\t\t\tcase 'month':\r\n\t\t\t\tdd.setMonth(dd.getMonth() + AddDayCount);\r\n\t\t\t\tbreak;\r\n\t\t\tcase 'year':\r\n\t\t\t\tdd.setFullYear(dd.getFullYear() + AddDayCount);\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\tconst y = dd.getFullYear();\r\n\t\tconst m = dd.getMonth() + 1;\r\n\t\tconst d = dd.getDate();\r\n\r\n\t\tlet nowDate = y + '-' + m + '-' + d\r\n\t\tconst lunarData = this.getlunar(y, m, d)\r\n\r\n\t\tconst dataObj : DateType = {\r\n\t\t\tfullDate: nowDate,\r\n\t\t\tyear: y,\r\n\t\t\tmonth: m,\r\n\t\t\tdate: d,\r\n\t\t\tday: dd.getDay() + 1,\r\n\t\t\tlunar: lunarData.IDayCn,\r\n\t\t\tis_today: lunarData.isToday,\r\n\t\t\tdisabled: false\r\n\t\t}\r\n\r\n\t\treturn dataObj\r\n\t}\r\n\r\n\t/**\r\n\t * 格式化年月显示 (xxxx/xx)\r\n\t */\r\n\tformatYearMonth(year: number, month: number): string {\r\n\t\tconst monthStr = month < 10 ? '0' + month : month.toString()\r\n\t\treturn `${year}/${monthStr}`\r\n\t}\r\n\r\n}"]}