{"version": 3, "sources": ["components/main-calendar-picker.uvue"], "names": [], "mappings": "AAiFC,OAAO,EAAE,QAAQ,EAAE,QAAO,EAAE,MAAO,sBAAqB,CAAA;AACxD,OAAO,mBAAkB,MAAO,8CAA6C,CAAA;AAE7E,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,sBAAsB;IAC5B,UAAU,EAAE;QACX,mBAAkB;KAClB;IACD,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;IAC5B,KAAK,EAAE;QACN,sBAAqB;QACrB,WAAW,EAAE;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,WAAG,EAAC;gBACZ,MAAM,KAAI,GAAI,IAAI,IAAI,EAAC,CAAA;gBACvB,MAAM,IAAG,GAAI,KAAK,CAAC,WAAW,EAAC,CAAA;gBAC/B,MAAM,KAAI,GAAI,KAAK,CAAC,QAAQ,EAAC,GAAI,CAAA,CAAA;gBACjC,MAAM,IAAG,GAAI,KAAK,CAAC,OAAO,EAAC,CAAA;gBAC3B,OAAO,GAAG,IAAI,IAAI,KAAI,GAAI,EAAC,CAAE,CAAA,CAAE,GAAE,GAAI,KAAI,CAAE,CAAA,CAAE,KAAK,IAAI,IAAG,GAAI,EAAC,CAAE,CAAA,CAAE,GAAE,GAAI,IAAG,CAAE,CAAA,CAAE,IAAI,EAAC,CAAA;YACrF,CAAA;SACD;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAQ;YACR,OAAO,EAAE,KAAI,IAAK,OAAO;YACzB,UAAS;YACT,QAAQ,EAAE,IAAI,QAAQ,EAAC,IAAK,QAAQ;YACpC,OAAM;YACN,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAC,IAAK,MAAM;YAC/C,OAAM;YACN,YAAY,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAC,GAAI,CAAC,CAAA,IAAK,MAAM;YACnD,UAAS;YACT,YAAY,EAAE,EAAC,IAAK,MAAM;YAC1B,QAAO;YACP,KAAK,EAAE,EAAC,IAAK,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACnC,OAAM;YACN,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAA,IAAK,MAAM,EAAC;SACzD,CAAA;IACD,CAAC;IACD,QAAQ,EAAE;QACT,WAAU;QACV,gBAAgB,IAAI,MAAK;YACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAA,CAAA;QACzE,CAAC;QACD,WAAU;QACV,gBAAgB,IAAI,MAAK;YACxB,IAAI,IAAI,CAAC,YAAW,KAAM,EAAE,EAAE;gBAC7B,OAAO,KAAI,CAAA;aACZ;YACA,MAAM,KAAI,GAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAA,CAAA;YACzC,IAAI,KAAK,CAAC,MAAK,IAAK,CAAC,EAAE;gBACtB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAE,CAAA;aACjE;YACA,OAAO,IAAI,CAAC,YAAW,CAAA;QACxB,CAAA;KACA;IACD,OAAO;QACN,IAAI,CAAC,cAAc,EAAC,CAAA;IACrB,CAAC;IACD,OAAO,EAAE;QACR,QAAO;QACP,cAAc;YACb,SAAQ;YACR,MAAM,SAAQ,GAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAA,CAAA;YAC5C,IAAI,SAAS,CAAC,MAAK,IAAK,CAAC,EAAE;gBAC1B,IAAI,CAAC,WAAU,GAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA,CAAA;gBACxC,IAAI,CAAC,YAAW,GAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA,CAAA;gBACzC,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,WAAU,CAAA;aACpC;YACA,IAAI,CAAC,cAAc,EAAC,CAAA;QACrB,CAAC;QAED,SAAQ;QACR,cAAc;YACb,MAAM,OAAM,GAAI,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,IAAG,CAAA;YAC3D,IAAI,CAAC,KAAI,GAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAA,CAAA;YAC3C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAA,8CAAA,CAAA,CAAA;QACvB,CAAC;QAED,YAAW;QACX,aAAa,CAAC,GAAG,EAAE,QAAQ,GAAG,OAAM;YACnC,IAAI,IAAI,CAAC,YAAW,KAAM,EAAC,IAAK,GAAG,CAAC,QAAQ,EAAE;gBAC7C,OAAO,KAAI,CAAA;aACZ;YACA,OAAO,GAAG,CAAC,QAAO,KAAM,IAAI,CAAC,YAAW,CAAA;QACzC,CAAC;QAED,SAAQ;QACR,WAAW,CAAC,GAAG,EAAE,QAAQ;YACxB,IAAI,GAAG,CAAC,QAAQ,EAAE;gBACjB,OAAK;aACN;YACA,IAAI,CAAC,YAAW,GAAI,GAAG,CAAC,QAAO,CAAA;QAChC,CAAC;QAED,MAAK;QACL,SAAS;YACR,IAAI,IAAI,CAAC,YAAW,IAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,WAAW,EAAC,CAAA;gBACjB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;aACtB;iBAAO;gBACN,IAAI,CAAC,YAAY,EAAC,CAAA;aACnB;YACA,IAAI,CAAC,cAAc,EAAC,CAAA;QACrB,CAAC;QAED,MAAK;QACL,SAAS;YACR,IAAI,IAAI,CAAC,YAAW,IAAK,EAAE,EAAE;gBAC5B,IAAI,CAAC,WAAW,EAAC,CAAA;gBACjB,IAAI,CAAC,YAAW,GAAI,CAAA,CAAA;aACrB;iBAAO;gBACN,IAAI,CAAC,YAAY,EAAC,CAAA;aACnB;YACA,IAAI,CAAC,cAAc,EAAC,CAAA;QACrB,CAAC;QAED,UAAS;QACT,mBAAmB;YAClB,MAAM,eAAc,GAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAA,IAAK,uBAAsB,CAAA;YAC/E,eAAe,CAAC,WAAW,CAAC,MAAM,CAAA,CAAA;QACnC,CAAC;QAED,SAAQ;QACR,kBAAkB,CAAC,aAAa,EAAE,aAAa;YAC9C,MAAM,IAAG,GAAI,aAAa,CAAC,SAAS,CAAC,MAAM,CAAA,CAAA;YAC3C,MAAM,KAAI,GAAI,aAAa,CAAC,SAAS,CAAC,OAAO,CAAA,CAAA;YAE7C,IAAI,IAAG,IAAK,IAAG,IAAK,KAAI,IAAK,IAAI,EAAE;gBAClC,IAAI,CAAC,WAAU,GAAI,IAAG,CAAA;gBACtB,IAAI,CAAC,YAAW,GAAI,KAAI,CAAA;gBACxB,IAAI,CAAC,cAAc,EAAC,CAAA;aACrB;QACD,CAAC;QAED,SAAQ;QACR,iBAAiB;YAChB,cAAa;QACd,CAAC;QAED,OAAM;QACN,IAAI;YACH,IAAI,CAAC,OAAM,GAAI,IAAG,CAAA;QACnB,CAAC;QAED,OAAM;QACN,KAAK;YACJ,IAAI,CAAC,OAAM,GAAI,KAAI,CAAA;QACpB,CAAC;QAED,YAAW;QACX,cAAc;YACb,IAAI,CAAC,KAAK,EAAC,CAAA;YACX,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAA,CAAA;QACpB,CAAC;QAED,WAAU;QACV,QAAQ;YACP,IAAI,CAAC,KAAK,EAAC,CAAA;YACX,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAA,CAAA;QACpB,CAAC;QAED,WAAU;QACV,SAAS;YACR,IAAI,IAAI,CAAC,YAAW,KAAM,EAAE,EAAE;gBAC7B,GAAG,CAAC,SAAS,CAAC;oBACb,KAAK,EAAE,OAAO;oBACd,IAAI,EAAE,MAAK;iBACX,CAAA,CAAA;gBACD,OAAK;aACN;YAEA,IAAI,CAAC,KAAK,EAAC,CAAA;YACX,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBACrB,IAAI,EAAE,IAAI,CAAC,YAAY;gBACvB,IAAI,EAAE,IAAI,CAAC,WAAW;gBACtB,KAAK,EAAE,IAAI,CAAC,YAAY;gBACxB,GAAG,EAAE,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;aAC7C,CAAA,CAAA;QACF,CAAA;KACD;CACD,CAAA,CAAA;;;;;;;eArQY,IAAA,CAAA,OAAO,CAAA;cAAnB,GAAA,CAkEO,MAAA,EAAA,GAAA,CAAA;;gBAlEc,KAAK,EAAC,gBAAgB;gBAAE,OAAK,EAAE,IAAA,CAAA,cAAc;;gBACjE,GAAA,CAgEO,MAAA,EAAA,GAAA,CAAA;oBAhED,KAAK,EAAC,cAAc;oBAAE,OAAK,EAAA,aAAA,CAAN,GAAA,EAAA,GAAA,CAAc,EAAA,CAAA,MAAA,CAAA,CAAA;;oBACxC,GAAA,CA8DO,MAAA,EAAA,GAAA,CAAA,EA9DD,KAAK,EAAC,2BAA2B,EAAA,CAAA,EAAA;wBAEtC,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,QAAQ,EAAA,CAAA,EAAA;4BACnB,GAAA,CAA4D,MAAA,EAAA,GAAA,CAAA;gCAAtD,KAAK,EAAC,oBAAoB;gCAAE,OAAK,EAAE,IAAA,CAAA,QAAQ;gCAAE,IAAE,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;4BACrD,GAAA,CAAmC,MAAA,EAAA,GAAA,CAAA,EAA7B,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,MAAI,CAAA;4BAC5B,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,uBAAuB,EAAA,CAAA,EAAA;gCAClC,GAAA,CAA8D,MAAA,EAAA,GAAA,CAAA;oCAAxD,KAAK,EAAC,qBAAqB;oCAAE,OAAK,EAAE,IAAA,CAAA,SAAS;oCAAE,IAAE,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;;;wBAKzD,GAAA,CAUO,MAAA,EAAA,GAAA,CAAA,EAVD,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAA;4BAC5B,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;gCAFD,KAAK,EAAC,eAAe;gCAAE,OAAK,EAAE,IAAA,CAAA,SAAS;;gCAC5C,GAAA,CAAgC,MAAA,EAAA,GAAA,CAAA,EAA1B,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,GAAC,CAAA;;4BAE1B,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;gCAFD,KAAK,EAAC,oBAAoB;gCAAE,OAAK,EAAE,IAAA,CAAA,mBAAmB;;gCAC3D,GAAA,CAA2D,MAAA,EAAA,GAAA,CAAA,EAArD,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,gBAAgB,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;4BAElD,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;gCAFD,KAAK,EAAC,eAAe;gCAAE,OAAK,EAAE,IAAA,CAAA,SAAS;;gCAC5C,GAAA,CAAgC,MAAA,EAAA,GAAA,CAAA,EAA1B,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,GAAC,CAAA;;;wBAK3B,GAAA,CAIO,MAAA,EAAA,GAAA,CAAA,EAJD,KAAK,EAAC,aAAa,EAAA,CAAA,EAAA;4BACxB,GAAA,CAEO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAFsB,IAAA,CAAA,QAAQ,EAAA,CAAvB,GAAG,EAAE,KAAK,EAAV,OAAG,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;uCAAjB,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;oCAFiC,GAAG,EAAE,KAAK;oCAAE,KAAK,EAAC,UAAU;;oCACnE,GAAA,CAA4C,MAAA,EAAA,GAAA,CAAA,EAAtC,KAAK,EAAC,eAAe,EAAA,CAAA,EAAA,GAAA,CAAI,GAAG,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;;;wBAKpC,GAAA,CAwBO,MAAA,EAAA,GAAA,CAAA,EAxBD,KAAK,EAAC,eAAe,EAAA,CAAA,EAAA;4BAC1B,GAAA,CAsBO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAtB2B,IAAA,CAAA,KAAK,EAAA,CAAzB,IAAI,EAAE,SAAS,EAAf,OAAI,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;uCAAlB,GAAA,CAsBO,MAAA,EAAA,GAAA,CAAA;oCAtBmC,GAAG,EAAE,SAAS;oCAAE,KAAK,EAAC,eAAe;;oCAC9E,GAAA,CAoBO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CApByB,IAAI,EAAA,CAAtB,GAAG,EAAE,QAAQ,EAAb,OAAG,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;+CAAjB,GAAA,CAoBO,MAAA,EAAA,GAAA,CAAA;4CApBgC,GAAG,EAAE,QAAQ;4CACnD,KAAK,EAAA,GAAA,CAAA,CAAC,cAAc,EACZ,GAAA,CAAA;;;;kDAIP,CAAA,CAAA;4CACA,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,WAAW,CAAC,GAAG,CAAA,CAAA,CAAA,CAAA;;4CACvB,GAAA,CAKyB,MAAA,EAAA,GAAA,CAAA;gDALnB,KAAK,EAAA,GAAA,CAAA,CAAC,YAAY,EACf,GAAA,CAAA;;;;sDAIP,CAAA,CAAA;oDAAK,GAAG,CAAC,IAAI,CAAA,EAAA,CAAA,CAAA,iBAAA,CAAA;4CACf,GAAA,CAK0B,MAAA,EAAA,GAAA,CAAA;gDALpB,KAAK,EAAA,GAAA,CAAA,CAAC,WAAW,EACd,GAAA,CAAA;;;;sDAIP,CAAA,CAAA;oDAAK,GAAG,CAAC,KAAK,CAAA,EAAA,CAAA,CAAA,iBAAA,CAAA;;;;;;wBAMnB,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA,EAHD,KAAK,EAAC,mBAAmB,EAAA,CAAA,EAAA;4BAC9B,GAAA,CAA0C,MAAA,EAAA,GAAA,CAAA,EAApC,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAC,OAAK,CAAA;4BACnC,GAAA,CAA2D,MAAA,EAAA,GAAA,CAAA,EAArD,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,gBAAgB,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;;;;;QAOrD,GAAA,CAMwB,gCAAA,EAAA,GAAA,CAAA;YALvB,GAAG,EAAC,iBAAiB;YACpB,cAAY,EAAE,IAAA,CAAA,WAAW;YACzB,eAAa,EAAE,IAAA,CAAA,YAAY;YAC3B,SAAO,EAAE,IAAA,CAAA,kBAAkB;YAC3B,QAAM,EAAE,IAAA,CAAA,iBAAiB", "file": "components/main-calendar-picker.uvue", "sourcesContent": ["<template>\n\t<!-- 弹窗遮罩层 -->\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\n\t\t\t<view class=\"calendar-picker-container\">\n\t\t\t\t<!-- 导航栏 -->\n\t\t\t\t<view class=\"navbar\">\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\n\t\t\t\t\t<text class=\"nav-title\">选择日期</text>\n\t\t\t\t\t<view class=\"confirm-btn-container\">\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 年月控制区域 -->\n\t\t\t\t<view class=\"calendar-header\">\n\t\t\t\t\t<view class=\"month-nav-btn\" @click=\"prevMonth\">\n\t\t\t\t\t\t<text class=\"nav-arrow\">‹</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"year-month-display\" @click=\"openYearMonthPicker\">\n\t\t\t\t\t\t<text class=\"year-month-text\">{{ currentYearMonth }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"month-nav-btn\" @click=\"nextMonth\">\n\t\t\t\t\t\t<text class=\"nav-arrow\">›</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 星期标题 -->\n\t\t\t\t<view class=\"week-header\">\n\t\t\t\t\t<view v-for=\"(day, index) in weekDays\" :key=\"index\" class=\"week-day\">\n\t\t\t\t\t\t<text class=\"week-day-text\">{{ day }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 日历网格 -->\n\t\t\t\t<view class=\"calendar-grid\">\n\t\t\t\t\t<view v-for=\"(week, weekIndex) in weeks\" :key=\"weekIndex\" class=\"calendar-week\">\n\t\t\t\t\t\t<view v-for=\"(day, dayIndex) in week\" :key=\"dayIndex\" \n\t\t\t\t\t\t\tclass=\"calendar-day\"\n\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t'day-disabled': day.disabled,\n\t\t\t\t\t\t\t\t'day-today': day.is_today,\n\t\t\t\t\t\t\t\t'day-selected': isSelectedDay(day)\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t@click=\"onDaySelect(day)\">\n\t\t\t\t\t\t\t<text class=\"day-number\" \n\t\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t\t'day-number-disabled': day.disabled,\n\t\t\t\t\t\t\t\t\t'day-number-today': day.is_today,\n\t\t\t\t\t\t\t\t\t'day-number-selected': isSelectedDay(day)\n\t\t\t\t\t\t\t\t}\">{{ day.date }}</text>\n\t\t\t\t\t\t\t<text class=\"day-lunar\"\n\t\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t\t'day-lunar-disabled': day.disabled,\n\t\t\t\t\t\t\t\t\t'day-lunar-today': day.is_today,\n\t\t\t\t\t\t\t\t\t'day-lunar-selected': isSelectedDay(day)\n\t\t\t\t\t\t\t\t}\">{{ day.lunar }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 当前选择显示 -->\n\t\t\t\t<view class=\"current-selection\">\n\t\t\t\t\t<text class=\"selection-label\">当前选择：</text>\n\t\t\t\t\t<text class=\"selection-value\">{{ selectedDateText }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\n\t<!-- 年月选择器 -->\n\t<main-yearmonth-picker \n\t\tref=\"yearmonthPicker\" \n\t\t:initial-year=\"currentYear\"\n\t\t:initial-month=\"currentMonth\"\n\t\t@confirm=\"onYearMonthConfirm\" \n\t\t@cancel=\"onYearMonthCancel\">\n\t</main-yearmonth-picker>\n</template>\n\n<script>\n\timport { Calendar, DateType } from '@/utils/calendar.uts'\n\timport MainYearmonthPicker from './main-form/tools/main-yearmonth-picker.uvue'\n\n\texport default {\n\t\tname: \"main-calendar-picker\",\n\t\tcomponents: {\n\t\t\tMainYearmonthPicker\n\t\t},\n\t\temits: ['cancel', 'confirm'],\n\t\tprops: {\n\t\t\t// 初始日期 (YYYY-MM-DD格式)\n\t\t\tinitialDate: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: () => {\n\t\t\t\t\tconst today = new Date()\n\t\t\t\t\tconst year = today.getFullYear()\n\t\t\t\t\tconst month = today.getMonth() + 1\n\t\t\t\t\tconst date = today.getDate()\n\t\t\t\t\treturn `${year}-${month < 10 ? '0' + month : month}-${date < 10 ? '0' + date : date}`\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 控制弹窗显示\n\t\t\t\tvisible: false as boolean,\n\t\t\t\t// 日历工具类实例\n\t\t\t\tcalendar: new Calendar() as Calendar,\n\t\t\t\t// 当前年份\n\t\t\t\tcurrentYear: new Date().getFullYear() as number,\n\t\t\t\t// 当前月份\n\t\t\t\tcurrentMonth: (new Date().getMonth() + 1) as number,\n\t\t\t\t// 当前选中的日期\n\t\t\t\tselectedDate: \"\" as string,\n\t\t\t\t// 日历周数据\n\t\t\t\tweeks: [] as Array<Array<DateType>>,\n\t\t\t\t// 星期标题\n\t\t\t\tweekDays: ['一', '二', '三', '四', '五', '六', '日'] as string[]\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 当前年月显示文本\n\t\t\tcurrentYearMonth(): string {\n\t\t\t\treturn this.calendar.formatYearMonth(this.currentYear, this.currentMonth)\n\t\t\t},\n\t\t\t// 选中日期显示文本\n\t\t\tselectedDateText(): string {\n\t\t\t\tif (this.selectedDate === \"\") {\n\t\t\t\t\treturn \"未选择\"\n\t\t\t\t}\n\t\t\t\tconst parts = this.selectedDate.split('-')\n\t\t\t\tif (parts.length == 3) {\n\t\t\t\t\treturn `${parts[0]}年${parseInt(parts[1])}月${parseInt(parts[2])}日`\n\t\t\t\t}\n\t\t\t\treturn this.selectedDate\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.initializeData()\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化数据\n\t\t\tinitializeData() {\n\t\t\t\t// 解析初始日期\n\t\t\t\tconst dateParts = this.initialDate.split('-')\n\t\t\t\tif (dateParts.length == 3) {\n\t\t\t\t\tthis.currentYear = parseInt(dateParts[0])\n\t\t\t\t\tthis.currentMonth = parseInt(dateParts[1])\n\t\t\t\t\tthis.selectedDate = this.initialDate\n\t\t\t\t}\n\t\t\t\tthis.updateCalendar()\n\t\t\t},\n\n\t\t\t// 更新日历数据\n\t\t\tupdateCalendar() {\n\t\t\t\tconst dateStr = `${this.currentYear}-${this.currentMonth}-1`\n\t\t\t\tthis.weeks = this.calendar.getWeeks(dateStr)\r\n\t\t\t\tconsole.log(this.weeks)\n\t\t\t},\n\n\t\t\t// 判断是否为选中日期\n\t\t\tisSelectedDay(day: DateType): boolean {\n\t\t\t\tif (this.selectedDate === \"\" || day.disabled) {\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\treturn day.fullDate === this.selectedDate\n\t\t\t},\n\n\t\t\t// 日期选择事件\n\t\t\tonDaySelect(day: DateType) {\n\t\t\t\tif (day.disabled) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.selectedDate = day.fullDate\n\t\t\t},\n\n\t\t\t// 上个月\n\t\t\tprevMonth() {\n\t\t\t\tif (this.currentMonth == 1) {\n\t\t\t\t\tthis.currentYear-- \n\t\t\t\t\tthis.currentMonth = 12\n\t\t\t\t} else {\n\t\t\t\t\tthis.currentMonth--\n\t\t\t\t}\n\t\t\t\tthis.updateCalendar()\n\t\t\t},\n\n\t\t\t// 下个月\n\t\t\tnextMonth() {\n\t\t\t\tif (this.currentMonth == 12) {\n\t\t\t\t\tthis.currentYear++\n\t\t\t\t\tthis.currentMonth = 1\n\t\t\t\t} else {\n\t\t\t\t\tthis.currentMonth++\n\t\t\t\t}\n\t\t\t\tthis.updateCalendar()\n\t\t\t},\n\n\t\t\t// 打开年月选择器\n\t\t\topenYearMonthPicker() {\n\t\t\t\tconst yearmonthPicker = this.$refs[\"yearmonthPicker\"] as ComponentPublicInstance\n\t\t\t\tyearmonthPicker.$callMethod(\"open\")\n\t\t\t},\n\n\t\t\t// 年月选择确认\n\t\t\tonYearMonthConfirm(yearMonthData: UTSJSONObject) {\n\t\t\t\tconst year = yearMonthData.getNumber(\"year\")\n\t\t\t\tconst month = yearMonthData.getNumber(\"month\")\n\t\t\t\t\n\t\t\t\tif (year != null && month != null) {\n\t\t\t\t\tthis.currentYear = year\n\t\t\t\t\tthis.currentMonth = month\n\t\t\t\t\tthis.updateCalendar()\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 年月选择取消\n\t\t\tonYearMonthCancel() {\n\t\t\t\t// 取消选择，不做任何操作\n\t\t\t},\n\n\t\t\t// 打开弹窗\n\t\t\topen() {\n\t\t\t\tthis.visible = true\n\t\t\t},\n\n\t\t\t// 关闭弹窗\n\t\t\tclose() {\n\t\t\t\tthis.visible = false\n\t\t\t},\n\n\t\t\t// 点击遮罩层关闭弹窗\n\t\t\tonOverlayClick() {\n\t\t\t\tthis.close()\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\n\t\t\t// 取消按钮点击事件\n\t\t\tonCancel() {\n\t\t\t\tthis.close()\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\n\t\t\t// 确定按钮点击事件\n\t\t\tonConfirm() {\n\t\t\t\tif (this.selectedDate === \"\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择日期',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.close()\n\t\t\t\tthis.$emit('confirm', {\n\t\t\t\t\tdate: this.selectedDate,\n\t\t\t\t\tyear: this.currentYear,\n\t\t\t\t\tmonth: this.currentMonth,\n\t\t\t\t\tday: parseInt(this.selectedDate.split('-')[2])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* 弹窗遮罩层 */\n\t.picker-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 1000;\n\t}\n\n\t.picker-modal {\n\t\twidth: 90%;\n\t\tmax-width: 700rpx;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n\t}\n\n\t.calendar-picker-container {\n\t\twidth: 100%;\n\t\tbackground-color: #ffffff;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t/* 导航栏样式 */\n\t.navbar {\n\t\theight: 44px;\n\t\tbackground-color: #f8f8f8;\n\t\tborder-bottom: 1px solid #e5e5e5;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 10px;\n\t}\n\n\t.nav-btn {\n\t\tfont-size: 16px;\n\t\tcolor: #007aff;\n\t\tpadding: 8px 12px;\n\t}\n\n\t.cancel-btn {\n\t\tcolor: #999999;\n\t}\n\n\t.confirm-btn-container {\n\t\theight: 30px;\n\t\tbackground-color: #007aff;\n\t\tborder-radius: 8rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\n\t}\n\n\t.confirm-btn {\n\t\tcolor: #ffffff;\n\t\tfont-weight: bold;\n\t}\n\n\t.nav-title {\n\t\tfont-size: 17px;\n\t\tcolor: #333333;\n\t}\n\n\t/* 年月控制区域 */\n\t.calendar-header {\n\t\theight: 80rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-bottom: 1px solid #e5e5e5;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 20rpx;\n\t}\n\n\t.month-nav-btn {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tbackground-color: rgba(0, 122, 255, 0.1);\n\t\tborder-radius: 30rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.nav-arrow {\n\t\tfont-size: 32rpx;\n\t\tcolor: #007aff;\n\t\tfont-weight: bold;\n\t}\n\n\t.year-month-display {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 0 20rpx;\n\t}\n\n\t.year-month-text {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t/* 星期标题 */\n\t.week-header {\n\t\theight: 60rpx;\n\t\tbackground-color: #f0f0f0;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\n\t.week-day {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.week-day-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t\tfont-weight: bold;\n\t}\n\n\t/* 日历网格 */\n\t.calendar-grid {\n\t\tpadding: 10rpx;\n\t\tbackground-color: #ffffff;\n\t}\n\n\t.calendar-week {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.calendar-day {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin: 0 2rpx;\n\t\tborder-radius: 8rpx;\n\t\tposition: relative;\n\t}\n\n\t.calendar-day.day-today {\n\t\tbackground-color: rgba(255, 69, 58, 0.1);\n\t\tborder: 2rpx solid #ff453a;\n\t}\n\n\t.calendar-day.day-selected {\n\t\tbackground-color: #007aff;\n\t\ttransform: scale(1.05);\n\t}\n\n\t.calendar-day.day-disabled {\n\t\topacity: 0.3;\n\t}\n\n\t.day-number {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: bold;\n\t\tline-height: 1;\n\t}\n\n\t.day-number-today {\n\t\tcolor: #ff453a;\n\t}\n\n\t.day-number-selected {\n\t\tcolor: #ffffff;\n\t}\n\n\t.day-number-disabled {\n\t\tcolor: #cccccc;\n\t}\n\n\t.day-lunar {\n\t\tfont-size: 20rpx;\n\t\tcolor: #999999;\n\t\tline-height: 1;\n\t\tmargin-top: 4rpx;\n\t}\n\n\t.day-lunar-today {\n\t\tcolor: #ff453a;\n\t}\n\n\t.day-lunar-selected {\n\t\tcolor: #ffffff;\n\t}\n\n\t.day-lunar-disabled {\n\t\tcolor: #cccccc;\n\t}\n\n\t/* 当前选择显示区域 */\n\t.current-selection {\n\t\tpadding: 20rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-top: 1px solid #e5e5e5;\n\t}\n\n\t.selection-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.selection-value {\n\t\tfont-size: 32rpx;\n\t\tcolor: #007aff;\n\t\tfont-weight: bold;\n\t}\n</style>\n"]}