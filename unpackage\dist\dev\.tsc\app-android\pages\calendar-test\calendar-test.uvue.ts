
	import MainCalendarPicker from '@/components/main-calendar-picker.uvue'

	const __sfc__ = defineComponent({
		name: "calendar-test",
		components: {
			MainCalendarPicker
		},
		data() {
			return {
				// 初始日期
				initialDate: "" as string,
				// 选择的日期
				selectedDate: "" as string
			}
		},
		created() {
			// 设置初始日期为今天
			const today = new Date()
			const year = today.getFullYear()
			const month = today.getMonth() + 1
			const date = today.getDate()
			this.initialDate = `${year}-${month < 10 ? '0' + month : month}-${date < 10 ? '0' + date : date}`
		},
		methods: {
			// 打开日历选择器
			openCalendarPicker() {
				const calendarPicker = this.$refs["calendarPicker"] as ComponentPublicInstance
				calendarPicker.$callMethod("open")
			},

			// 日历选择确认
			onCalendarConfirm(dateData: UTSJSONObject) {
				console.log('选择的日期:', dateData, " at pages/calendar-test/calendar-test.uvue:63")
				const date = dateData.getString("date")
				if (date != null) {
					this.selectedDate = date
				}
				
				uni.showToast({
					title: `已选择: ${this.selectedDate}`,
					icon: 'success'
				})
			},

			// 日历选择取消
			onCalendarCancel() {
				console.log('取消选择日期', " at pages/calendar-test/calendar-test.uvue:77")
				uni.showToast({
					title: '取消选择',
					icon: 'none'
				})
			}
		}
	})

export default __sfc__
function GenPagesCalendarTestCalendarTestRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_main_calendar_picker = resolveComponent("main-calendar-picker")

  return _cE("scroll-view", _uM({ class: "container" }), [
    _cE("view", _uM({ class: "content" }), [
      _cE("text", _uM({ class: "title" }), "日历弹窗测试"),
      _cE("view", _uM({ class: "test-section" }), [
        _cE("button", _uM({
          class: "test-btn",
          onClick: _ctx.openCalendarPicker
        }), "打开日历选择器", 8 /* PROPS */, ["onClick"]),
        _cE("view", _uM({ class: "result-section" }), [
          _cE("text", _uM({ class: "result-label" }), "选择的日期："),
          _cE("text", _uM({ class: "result-value" }), _tD(_ctx.selectedDate), 1 /* TEXT */)
        ])
      ])
    ]),
    _cV(_component_main_calendar_picker, _uM({
      ref: "calendarPicker",
      "initial-date": _ctx.initialDate,
      onConfirm: _ctx.onCalendarConfirm,
      onCancel: _ctx.onCalendarCancel
    }), null, 8 /* PROPS */, ["initial-date", "onConfirm", "onCancel"])
  ])
}
const GenPagesCalendarTestCalendarTestStyles = [_uM([["container", _pS(_uM([["flex", 1], ["backgroundColor", "#f5f5f5"]]))], ["content", _pS(_uM([["paddingTop", "40rpx"], ["paddingRight", "40rpx"], ["paddingBottom", "40rpx"], ["paddingLeft", "40rpx"], ["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"]]))], ["title", _pS(_uM([["fontSize", "48rpx"], ["fontWeight", "bold"], ["color", "#333333"], ["marginBottom", "60rpx"]]))], ["test-section", _pS(_uM([["width", "100%"], ["maxWidth", "600rpx"], ["backgroundColor", "#ffffff"], ["borderTopLeftRadius", "20rpx"], ["borderTopRightRadius", "20rpx"], ["borderBottomRightRadius", "20rpx"], ["borderBottomLeftRadius", "20rpx"], ["paddingTop", "40rpx"], ["paddingRight", "40rpx"], ["paddingBottom", "40rpx"], ["paddingLeft", "40rpx"], ["boxShadow", "0 4rpx 20rpx rgba(0, 0, 0, 0.1)"]]))], ["test-btn", _pS(_uM([["width", "100%"], ["height", "80rpx"], ["backgroundColor", "#007aff"], ["color", "#ffffff"], ["borderTopLeftRadius", "12rpx"], ["borderTopRightRadius", "12rpx"], ["borderBottomRightRadius", "12rpx"], ["borderBottomLeftRadius", "12rpx"], ["fontSize", "32rpx"], ["fontWeight", "bold"], ["borderTopWidth", "medium"], ["borderRightWidth", "medium"], ["borderBottomWidth", "medium"], ["borderLeftWidth", "medium"], ["borderTopStyle", "none"], ["borderRightStyle", "none"], ["borderBottomStyle", "none"], ["borderLeftStyle", "none"], ["borderTopColor", "#000000"], ["borderRightColor", "#000000"], ["borderBottomColor", "#000000"], ["borderLeftColor", "#000000"], ["marginBottom", "40rpx"]]))], ["result-section", _pS(_uM([["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"], ["paddingTop", "30rpx"], ["paddingRight", "30rpx"], ["paddingBottom", "30rpx"], ["paddingLeft", "30rpx"], ["backgroundColor", "#f8f9fa"], ["borderTopLeftRadius", "12rpx"], ["borderTopRightRadius", "12rpx"], ["borderBottomRightRadius", "12rpx"], ["borderBottomLeftRadius", "12rpx"]]))], ["result-label", _pS(_uM([["fontSize", "28rpx"], ["color", "#666666"], ["marginBottom", "10rpx"]]))], ["result-value", _pS(_uM([["fontSize", "36rpx"], ["color", "#007aff"], ["fontWeight", "bold"]]))]])]
