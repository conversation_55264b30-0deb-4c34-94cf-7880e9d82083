{"version": 3, "sources": ["pages/calendar-test/calendar-test.uvue"], "names": [], "mappings": "AA8BC,OAAO,kBAAiB,MAAO,wCAAuC,CAAA;AAEtE,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,eAAe;IACrB,UAAU,EAAE;QACX,kBAAiB;KACjB;IACD,IAAI;QACH,OAAO;YACN,OAAM;YACN,WAAW,EAAE,EAAC,IAAK,MAAM;YACzB,QAAO;YACP,YAAY,EAAE,EAAC,IAAK,MAAK;SAC1B,CAAA;IACD,CAAC;IACD,OAAO;QACN,YAAW;QACX,MAAM,KAAI,GAAI,IAAI,IAAI,EAAC,CAAA;QACvB,MAAM,IAAG,GAAI,KAAK,CAAC,WAAW,EAAC,CAAA;QAC/B,MAAM,KAAI,GAAI,KAAK,CAAC,QAAQ,EAAC,GAAI,CAAA,CAAA;QACjC,MAAM,IAAG,GAAI,KAAK,CAAC,OAAO,EAAC,CAAA;QAC3B,IAAI,CAAC,WAAU,GAAI,GAAG,IAAI,IAAI,KAAI,GAAI,EAAC,CAAE,CAAA,CAAE,GAAE,GAAI,KAAI,CAAE,CAAA,CAAE,KAAK,IAAI,IAAG,GAAI,EAAC,CAAE,CAAA,CAAE,GAAE,GAAI,IAAG,CAAE,CAAA,CAAE,IAAI,EAAC,CAAA;IACjG,CAAC;IACD,OAAO,EAAE;QACR,UAAS;QACT,kBAAkB;YACjB,MAAM,cAAa,GAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAA,IAAK,uBAAsB,CAAA;YAC7E,cAAc,CAAC,WAAW,CAAC,MAAM,CAAA,CAAA;QAClC,CAAC;QAED,SAAQ;QACR,iBAAiB,CAAC,QAAQ,EAAE,aAAa;YACxC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAA,+CAAA,CAAA,CAAA;YAC9B,MAAM,IAAG,GAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAA,CAAA;YACtC,IAAI,IAAG,IAAK,IAAI,EAAE;gBACjB,IAAI,CAAC,YAAW,GAAI,IAAG,CAAA;aACxB;YAEA,GAAG,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,QAAQ,IAAI,CAAC,YAAY,EAAE;gBAClC,IAAI,EAAE,SAAQ;aACd,CAAA,CAAA;QACF,CAAC;QAED,SAAQ;QACR,gBAAgB;YACf,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAA,+CAAA,CAAA,CAAA;YACpB,GAAG,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,MAAM;gBACb,IAAI,EAAE,MAAK;aACX,CAAA,CAAA;QACF,CAAA;KACD;CACD,CAAA,CAAA;;;;;;WAjFA,GAAA,CAuBc,aAAA,EAAA,GAAA,CAAA,EAvBD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;QAE7B,GAAA,CAWO,MAAA,EAAA,GAAA,CAAA,EAXD,KAAK,EAAC,SAAS,EAAA,CAAA,EAAA;YACpB,GAAA,CAAiC,MAAA,EAAA,GAAA,CAAA,EAA3B,KAAK,EAAC,OAAO,EAAA,CAAA,EAAC,QAAM,CAAA;YAE1B,GAAA,CAOO,MAAA,EAAA,GAAA,CAAA,EAPD,KAAK,EAAC,cAAc,EAAA,CAAA,EAAA;gBACzB,GAAA,CAAqE,QAAA,EAAA,GAAA,CAAA;oBAA7D,KAAK,EAAC,UAAU;oBAAE,OAAK,EAAE,IAAA,CAAA,kBAAkB;oBAAE,SAAO,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;gBAE5D,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA,EAHD,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;oBAC3B,GAAA,CAAwC,MAAA,EAAA,GAAA,CAAA,EAAlC,KAAK,EAAC,cAAc,EAAA,CAAA,EAAC,QAAM,CAAA;oBACjC,GAAA,CAAqD,MAAA,EAAA,GAAA,CAAA,EAA/C,KAAK,EAAC,cAAc,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,YAAY,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;;;QAM7C,GAAA,CAKuB,+BAAA,EAAA,GAAA,CAAA;YAJtB,GAAG,EAAC,gBAAgB;YACnB,cAAY,EAAE,IAAA,CAAA,WAAW;YACzB,SAAO,EAAE,IAAA,CAAA,iBAAiB;YAC1B,QAAM,EAAE,IAAA,CAAA,gBAAgB", "file": "pages/calendar-test/calendar-test.uvue", "sourcesContent": ["<template>\n\n\t<scroll-view class=\"container\">\n\n\t\t<view class=\"content\">\n\t\t\t<text class=\"title\">日历弹窗测试</text>\n\t\t\t\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<button class=\"test-btn\" @click=\"openCalendarPicker\">打开日历选择器</button>\n\t\t\t\t\n\t\t\t\t<view class=\"result-section\">\n\t\t\t\t\t<text class=\"result-label\">选择的日期：</text>\n\t\t\t\t\t<text class=\"result-value\">{{ selectedDate  }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 日历选择器 -->\n\t\t<main-calendar-picker \n\t\t\tref=\"calendarPicker\" \n\t\t\t:initial-date=\"initialDate\"\n\t\t\t@confirm=\"onCalendarConfirm\" \n\t\t\t@cancel=\"onCalendarCancel\">\n\t\t</main-calendar-picker>\n\n\t</scroll-view>\n\n</template>\n\n<script>\n\timport MainCalendarPicker from '@/components/main-calendar-picker.uvue'\n\n\texport default {\n\t\tname: \"calendar-test\",\n\t\tcomponents: {\n\t\t\tMainCalendarPicker\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 初始日期\n\t\t\t\tinitialDate: \"\" as string,\n\t\t\t\t// 选择的日期\n\t\t\t\tselectedDate: \"\" as string\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 设置初始日期为今天\n\t\t\tconst today = new Date()\n\t\t\tconst year = today.getFullYear()\n\t\t\tconst month = today.getMonth() + 1\n\t\t\tconst date = today.getDate()\n\t\t\tthis.initialDate = `${year}-${month < 10 ? '0' + month : month}-${date < 10 ? '0' + date : date}`\n\t\t},\n\t\tmethods: {\n\t\t\t// 打开日历选择器\n\t\t\topenCalendarPicker() {\n\t\t\t\tconst calendarPicker = this.$refs[\"calendarPicker\"] as ComponentPublicInstance\n\t\t\t\tcalendarPicker.$callMethod(\"open\")\n\t\t\t},\n\n\t\t\t// 日历选择确认\n\t\t\tonCalendarConfirm(dateData: UTSJSONObject) {\n\t\t\t\tconsole.log('选择的日期:', dateData)\n\t\t\t\tconst date = dateData.getString(\"date\")\n\t\t\t\tif (date != null) {\n\t\t\t\t\tthis.selectedDate = date\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `已选择: ${this.selectedDate}`,\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 日历选择取消\n\t\t\tonCalendarCancel() {\n\t\t\t\tconsole.log('取消选择日期')\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '取消选择',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tflex: 1;\n\t\tbackground-color: #f5f5f5;\n\t}\n\n\t.content {\n\t\tpadding: 40rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\n\t.title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 60rpx;\n\t}\n\n\t.test-section {\n\t\twidth: 100%;\n\t\tmax-width: 600rpx;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.test-btn {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tbackground-color: #007aff;\n\t\tcolor: #ffffff;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tborder: none;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.result-section {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.result-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.result-value {\n\t\tfont-size: 36rpx;\n\t\tcolor: #007aff;\n\t\tfont-weight: bold;\n\t}\n</style>\n"]}