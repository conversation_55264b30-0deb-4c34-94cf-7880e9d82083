// 农历信息类型
export type LunarInfoType = {
	lYear: number;
	lMonth: number;
	lDay: number;
	IMonthCn: string;
	IDayCn: string;
	cYear: number;
	cMonth: number;
	cDay: number;
	gzYear?: string;
	gzMonth?: string;
	gzDay?: string;
	isToday: boolean;
	isLeap: boolean;
	nWeek?: number;
	ncWeek?: string;
	isTerm?: boolean;
	Term?: string;
	astro?: string
}

// 日期类型
export type DateType = {
	fullDate: string;
	year: number;
	month: number;
	date: number;
	day: number;
	disabled: boolean;
	lunar: string;
	is_today: boolean;
	data?: LunarInfoType;
	// 农历信息
	lYear: number;
	lMonth: number;
	lDay: number;
	lMonthCn: string;  // 农历月份中文，如"七月"
	lDayCn: string;    // 农历日期中文，如"初十"
	lunarDate: string; // 完整农历日期，如"七月初十"
	// 节日信息
	festival: string | null;      // 阳历节日
	lunarFestival: string | null; // 农历节日
	// 星期信息
	nWeek: number;     // 数字星期(1-7，周一到周日)
	ncWeek: string;    // 中文星期，如"星期一"
	// 节气信息
	isTerm: boolean;
	Term: string | null;
	// 其他信息
	isLeap: boolean;   // 是否闰月
	Animal: string;    // 生肖
	astro: string;     // 星座
}

// 农历信息辅助类型
export type InfoType = {
	lunarY: number;
	lunarM: number;
	lunarD: number;
	isLeap: boolean;
}

// 节日类型
type FestivalType = {
	title: string;
}

/**
 * 农历1900-2100的润大小信息表
 * @Array Of Property
 * @return Hex
 */
const lunarYears = [
	0x04bd8,
	// 1901-2000
	0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2, 0x04ae0,
	0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977, 0x04970,
	0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970, 0x06566,
	0x0d4a0, 0x0ea50, 0x16a95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950, 0x0d4a0,
	0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557, 0x06ca0,
	0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0, 0x0aea6,
	0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0, 0x096d0,
	0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6, 0x095b0,
	0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570, 0x04af5,
	0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x05ac0, 0x0ab60, 0x096d5, 0x092e0, 0x0c960,
	// 2001-2100
	0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5, 0x0a950,
	0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930, 0x07954,
	0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530, 0x05aa0,
	0x076a3, 0x096d0, 0x04afb, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45, 0x0b5a0,
	0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0, 0x14b63,
	0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0, 0x092e0,
	0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4, 0x052d0,
	0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0, 0x0b273,
	0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160, 0x0e968,
	0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252, 0x0d520
]

// ['月','正','一','二','三','四','五','六','七','八','九','十','冬','腊'];
const N_STR_3 = ["\u6708", "\u6b63", "\u4e8c", "\u4e09", "\u56db", "\u4e94", "\u516d", "\u4e03", "\u516b", "\u4e5d", "\u5341", "\u51ac", "\u814a"]
// ['日','一','二','三','四','五','六','七','八','九','十']
const N_STR_1 = ["\u65e5", "\u4e00", "\u4e8c", "\u4e09", "\u56db", "\u4e94", "\u516d", "\u4e03", "\u516b", "\u4e5d", "\u5341"]
// ['初','十','廿','卅','闰']
const N_STR_2 = ["\u521d", "\u5341", "\u5eff", "\u5345", "\u95f0"]

// 生肖数组
const Animals = ["\u9f20", "\u725b", "\u864e", "\u5154", "\u9f99", "\u86c7", "\u9a6c", "\u7f8a", "\u7334", "\u9e21", "\u72d7", "\u732a"]

/**
 * 阳历节日
 */
const festival = new Map<string, FestivalType>([
	['1-1', { title: '元旦节1' }],
	['2-14', { title: '情人节' }],
	['3-8', { title: '妇女节' }],
	['3-12', { title: '植树节' }],
	['4-1', { title: '愚人节' }],
	['4-4', { title: '清明节' }],
	['5-1', { title: '劳动节' }],
	['5-4', { title: '青年节' }],
	['5-12', { title: '护士节' }],
	['6-1', { title: '儿童节' }],
	['7-1', { title: '建党节' }],
	['8-1', { title: '建军节' }],
	['9-10', { title: '教师节' }],
	['10-1', { title: '国庆节' }],
	['12-24', { title: '平安夜' }],
	['12-25', { title: '圣诞节' }]
])

/**
 * 农历节日
 */
const lfestival = new Map<string, FestivalType>([
	['12-30', { title: '除夕' }],
	['1-1', { title: '春节' }],
	['1-15', { title: '元宵节' }],
	['2-2', { title: '龙抬头' }],
	['5-5', { title: '端午节' }],
	['7-7', { title: '七夕节' }],
	['7-15', { title: '中元节' }],
	['8-15', { title: '中秋节' }],
	['9-9', { title: '重阳节' }],
	['10-1', { title: '寒衣节' }],
	['10-15', { title: '下元节' }],
	['12-8', { title: '腊八节' }],
	['12-23', { title: '北方小年' }],
	['12-24', { title: '南方小年' }]
])

/**
 * 24节气速查表
 */
const solarTerm = ["\u5c0f\u5bd2", "\u5927\u5bd2", "\u7acb\u6625", "\u96e8\u6c34", "\u60ca\u86f0", "\u6625\u5206",
	"\u6e05\u660e", "\u8c37\u96e8", "\u7acb\u590f", "\u5c0f\u6ee1", "\u8292\u79cd", "\u590f\u81f3",
	"\u5c0f\u6691", "\u5927\u6691", "\u7acb\u79cb", "\u5904\u6691", "\u767d\u9732", "\u79cb\u5206",
	"\u5bd2\u9732", "\u971c\u964d", "\u7acb\u51ac", "\u5c0f\u96ea", "\u5927\u96ea", "\u51ac\u81f3"]

/**
 * 1900-2100各年的24节气日期速查表
 */
const sTermInfo = ['9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
	'97bcf97c3598082c95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa',
	'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f',
	'b027097bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',
	'97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa', '9778397bd19801ec9210c965cc920e',
	'97b6b97bd19801ec95f8c965cc920f', '97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2',
	'9778397bd197c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e', '97bd09801d98082c95f8e1cfcc920f',
	'97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e',
	'97bcf97c3598082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa',
	'97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',
	'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f',
	'97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
	'97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
	'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',
	'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',
	'97bd097bd07f595b0b6fc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '9778397bd19801ec9210c9274c920e',
	'97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',
	'9778397bd097c36c9210c9274c920e', '97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722',
	'7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e',
	'97bd07f1487f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa',
	'97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',
	'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722',
	'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
	'97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
	'97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b6fc920fb0722',
	'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722',
	'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c91aa', '97b6b97bd197c36c9210c9274c920e',
	'97bcf7f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2',
	'9778397bd097c36c9210c9274c920e', '97b6b7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722',
	'7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b70c9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',
	'7f0e37f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa',
	'97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',
	'9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',
	'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',
	'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
	'97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',
	'9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722',
	'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c91aa', '97b6b7f0e47f149b0723b0787b0721',
	'7f0e27f0e47f531b0723b0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2',
	'977837f0e37f149b0723b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722',
	'7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0721',
	'7f0e37f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b06bd',
	'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',
	'977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',
	'7f0e397bd097c35b0b6fc920fb0722', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
	'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd',
	'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',
	'977837f0e37f14998082b0787b06bd', '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722',
	'7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0723b06bd', '7f07e7f0e37f149b0723b0787b0721',
	'7f0e27f0e47f531b0723b0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14898082b0723b02d5',
	'7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f595b0b0bb0b6fb0722',
	'7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722',
	'7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd',
	'7f07e7f0e47f531b0723b0b6fb0721', '7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35',
	'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',
	'7f0e37f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
	'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0787b06bd',
	'7f07e7f0e47f149b0723b0787b0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35',
	'7ec967f0e37f14998082b0723b06bd', '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',
	'7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0723b06bd', '7f07e7f0e37f14998083b0787b0721',
	'7f0e27f0e47f531b0723b0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14898082b0723b02d5',
	'7f07e7f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66aa89801e9808297c35',
	'665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722',
	'7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd',
	'7f07e7f0e47f531b0723b0b6fb0721', '7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b072297c35',
	'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e26665b66a449801e9808297c35',
	'665f67f0e37f1489801eb072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
	'7f0e27f1487f531b0b0bb0b6fb0722']

// 完整的农历类
export class Lunar {
	private lunarYearDaysMap = new Map<number, number>()
	private lunarMonthDaysMap = new Map<number, number[]>()

	constructor() { }

	/**
	 * 传入农历数字月份返回汉语通俗表示法
	 * @param lunar month
	 * @return Cn string
	 * @eg:let cnMonth = calendar.toChinaMonth(12) ;//cnMonth='腊月'
	 */
	toChinaMonth(m: number, leap: boolean = false): string { // 月 => \u6708
		return leap ? (N_STR_3[4] + N_STR_3[m] + N_STR_3[0]) : (N_STR_3[m] + N_STR_3[0]);
	}

	/**
	 * 传入农历日期数字返回汉字表示法
	 * @param lunar day
	 * @return Cn string
	 * @eg:let cnDay = calendar.toChinaDay(21) ;//cnMonth='廿一'
	 */
	toChinaDay(d: number): string { // 日 => \u65e5
		let s: string
		switch (d) {
			case 10:
				s = '\u521d\u5341';
				break
			case 20:
				s = '\u4e8c\u5341';
				break
			case 30:
				s = '\u4e09\u5341';
				break
			default:
				s = N_STR_2[Math.floor(d / 10)]
				s += N_STR_1[d % 10]
		}
		return (s)
	}

	/**
	 * 返回农历y年闰月是哪个月；若y年没有闰月 则返回0
	 * @param lunar Year
	 * @return Number (0-12)
	 * @eg:let leapMonth = calendar.leapMonth(1987) ;//leapMonth=6
	 */
	leapMonth(year: number): number {
		return lunarYears[year - 1900] & 0xF;
	}

	/**
	 * 返回农历y年闰月的天数 若该年没有闰月则返回0
	 * @param lunar Year
	 * @return Number (0、29、30)
	 * @eg:let leapMonthDay = calendar.leapDays(1987) ;//leapMonthDay=29
	 */
	leapDays(year: number): number {
		if (this.leapMonth(year) > 0) {
			return (lunarYears[year - 1900] & 0x10000) != 0 ? 30 : 29;
		}
		return 0;
	}

	// 某年份农历各月天数
	lunarMonthDays(year: number): number[] {
		let monthDays = this.lunarMonthDaysMap.get(year)
		if (monthDays != null) {
			return monthDays
		}

		monthDays = [];

		let lunarYear = lunarYears[year - 1900];

		for (let i = 15; i >= 4; i--) {
			let monthDay = (lunarYear >> i & 0x1) != 0 ? 30 : 29;
			monthDays.push(monthDay);
		}

		// 添加闰月
		let leapM = this.leapMonth(year);

		if (leapM > 0) monthDays.splice(leapM, 0, this.leapDays(year));
		this.lunarMonthDaysMap.set(year, monthDays)

		return monthDays;
	}

	// 某年农历天数
	lunarYearDays(year: number): number {
		if (this.lunarYearDaysMap.has(year)) {
			return this.lunarYearDaysMap.get(year)!
		}
		let num = 0;
		this.lunarMonthDays(year).forEach(item => {
			num += item;
		});
		this.lunarYearDaysMap.set(year, num)
		return num;
	}

	/**
	 * 传入阳历年月日获得详细的公历、农历object信息 <=>JSON
	 * @param y  solar year
	 * @param m  solar month
	 * @param d  solar day
	 * @return JSON object
	 * @eg:__f__('log','at utils/calendar.uts:341',calendar.solar2lunar(1987,11,01));
	 */
	solar2lunar(y: number, m: number, d: number): LunarInfoType { // 参数区间1900.1.31~2100.12.31
		let moonDay = this.solar_date(y, m, d);
		let lYear = moonDay.lunarY
		let lMonth = moonDay.lunarM
		let lDay = moonDay.lunarD
		let isLeap = moonDay.isLeap

		// 计算农历日期
		const IMonthCn = this.toChinaMonth(lMonth, isLeap)

		// 检查阳历节日
		const festivalKey = `${m}-${d}`
		const solarFestival = festival.get(festivalKey)

		// 检查农历节日
		const lunarFestivalKey = `${lMonth}-${lDay}`
		const lunarFestival = lfestival.get(lunarFestivalKey)

		// 检查24节气
		const firstNode = this.getTerm(y, (m * 2 - 1)); // 返回当月「节」为几日开始
		const secondNode = this.getTerm(y, (m * 2)); // 返回当月「气」为几日开始
		let isTerm = false;
		let Term: string | null = null;
		if (firstNode == d) {
			isTerm = true;
			Term = solarTerm[m * 2 - 2];
		}
		if (secondNode == d) {
			isTerm = true;
			Term = solarTerm[m * 2 - 1];
		}

		// 优先级：节气 > 阳历节日 > 农历节日 > 农历日期
		let IDayCn: string
		if (isTerm && Term != null) {
			IDayCn = Term
		} else if (solarFestival != null) {
			IDayCn = solarFestival.title
		} else if (lunarFestival != null) {
			IDayCn = lunarFestival.title
		} else if (lDay == 1) {
			IDayCn = IMonthCn
		} else {
			IDayCn = this.toChinaDay(lDay)
		}

		// 是否今天
		let isTodayObj = new Date()
		let isToday = false
		if (isTodayObj.getFullYear() == y && isTodayObj.getMonth() + 1 == m && isTodayObj.getDate() == d) {
			isToday = true
		}

		// 星期几计算
		const dateObj = new Date(y, m - 1, d)
		let nWeek = dateObj.getDay()
		if (nWeek == 0) {
			nWeek = 7  // 周日改为7
		}
		const weekNames = ["\u65e5", "\u4e00", "\u4e8c", "\u4e09", "\u56db", "\u4e94", "\u516d"]
		const cWeek = weekNames[dateObj.getDay()]
		const ncWeek = "\u661f\u671f" + cWeek

		let info: LunarInfoType = {
			'lYear': lYear,
			'lMonth': lMonth,
			'lDay': lDay,
			'IMonthCn': IMonthCn,
			'IDayCn': IDayCn,
			'cYear': y,
			'cMonth': m,
			'cDay': d,
			'isToday': isToday,
			'isLeap': isLeap,
			'isTerm': isTerm,
			'Term': Term,
			'nWeek': nWeek,
			'ncWeek': ncWeek,
			'astro': this.toAstro(m, d)
		}
		return info
	}

	solar_date(y: number, m: number, d: number): InfoType { // 参数区间1900.1.31~2100.12.31
		let date = new Date(y, m - 1, d);

		// 参照日期 1901-02-19 正月初一
		let offset = (Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()) - Date.UTC(1901, 1, 19)) / 86400000;
		let temp = 0
		let i: number;
		for (i = 1901; i < 2101 && offset > 0; i++) {
			temp = this.lunarYearDays(i);
			offset -= temp;
		}
		if (offset < 0) {
			offset += temp;
			i--;
		}

		// 农历年、月、日
		let isLeap: boolean = false
		let j: number = 0;
		let monthDays = this.lunarMonthDays(i);
		let leapM = this.leapMonth(i);

		if (offset > 0) {
			for (j = 0; j < monthDays.length && offset > 0; j++) {
				temp = monthDays[j];
				offset -= temp;
			}
			if (offset == 0) {
				j++;
			}
			if (offset < 0) {
				offset += temp;
			}
		} else {
			// 补偿公历1901年2月的农历信息
			if (offset == -23) {
				let info: InfoType = {
					lunarY: i,
					lunarM: 12,
					lunarD: 8,
					isLeap: false
				}
				info = info
			}
		}

		// 矫正闰年月
		if (leapM > 0) {
			if (j == leapM + 1) {
				isLeap = true
			}
			if (j >= leapM + 1) {
				j--
			}
		}
		const info: InfoType = {
			lunarY: i,
			lunarM: j,
			lunarD: ++offset,
			isLeap: isLeap
		}

		return info
	}

	/**
	 * 传入公历年获得该年第n个节气的公历日期
	 * @param y公历年(1900-2100)；n二十四节气中的第几个节气(1~24)；从n=1(小寒)算起
	 * @return day Number
	 * @eg:var _24 = calendar.getTerm(1987,3) ;//_24=4;意即1987年2月4日立春
	 */
	getTerm(y: number, n: number): number {
		if (y < 1900 || y > 2100) {
			return -1;
		}
		if (n < 1 || n > 24) {
			return -1;
		}
		const _table = sTermInfo[y - 1900];
		const _info = [
			parseInt('0x' + _table.substring(0, 5)).toString(),
			parseInt('0x' + _table.substring(5, 10)).toString(),
			parseInt('0x' + _table.substring(10, 15)).toString(),
			parseInt('0x' + _table.substring(15, 20)).toString(),
			parseInt('0x' + _table.substring(20, 25)).toString(),
			parseInt('0x' + _table.substring(25, 30)).toString()
		];
		const _calday = [
			_info[0].substring(0, 1),
			_info[0].substring(1, 3),
			_info[0].substring(3, 4),
			_info[0].substring(4, 6),

			_info[1].substring(0, 1),
			_info[1].substring(1, 3),
			_info[1].substring(3, 4),
			_info[1].substring(4, 6),

			_info[2].substring(0, 1),
			_info[2].substring(1, 3),
			_info[2].substring(3, 4),
			_info[2].substring(4, 6),

			_info[3].substring(0, 1),
			_info[3].substring(1, 3),
			_info[3].substring(3, 4),
			_info[3].substring(4, 6),

			_info[4].substring(0, 1),
			_info[4].substring(1, 3),
			_info[4].substring(3, 4),
			_info[4].substring(4, 6),

			_info[5].substring(0, 1),
			_info[5].substring(1, 3),
			_info[5].substring(3, 4),
			_info[5].substring(4, 6),
		];
		return parseInt(_calday[n - 1]);
	}

	/**
	 * 年份转生肖[!仅能大致转换] => 精确划分生肖分界线是"立春"
	 * @param y year
	 * @return Cn string
	 */
	getAnimal(y: number): string {
		return Animals[(y - 4) % 12]
	}

	/**
	 * 公历月、日判断所属星座
	 * @param cMonth
	 * @param cDay
	 * @return Cn string
	 */
	toAstro(cMonth: number, cDay: number): string {
		const s = "\u9b54\u7faf\u6c34\u74f6\u53cc\u9c7c\u767d\u7f8a\u91d1\u725b\u53cc\u5b50\u5de8\u87f9\u72ee\u5b50\u5904\u5973\u5929\u79e4\u5929\u874e\u5c04\u624b\u9b54\u7faf";
		const arr = [20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22];
		return s.substring(cMonth * 2 - (cDay < arr[cMonth - 1] ? 2 : 0), cMonth * 2 - (cDay < arr[cMonth - 1] ? 2 : 0) + 2) + "\u5ea7";
	}
}

// 日历工具类
export class Calendar {
	private lunar: Lunar
	
	constructor() {
		this.lunar = new Lunar()
	}

	getDateInfo(time: string = ''): DateType {
		const nowDate = this.getDate(time)
		const lunar = this.getlunar(nowDate.year, nowDate.month, nowDate.date)
		const item: DateType = nowDate
		item.data = lunar
		return item
	}

	/**
	 * 获取每周数据
	 * @param {string} dateData
	 */
	getWeeks(dateData: string = ''): Array<Array<DateType>> {
		const dateObj = this.getDate(dateData)
		const year = dateObj.year
		const month = dateObj.month
		let firstDay = new Date(year, month - 1, 0).getDay()
		// 获取本月天数
		let currentDay = new Date(year, month, 0).getDate()
		// 上个月末尾几天
		const lastMonthDays = this._getLastMonthDays(firstDay, dateObj)
		// 本月天数
		const currentMonthDys = this._currentMonthDys(currentDay, dateObj)
		// 本月剩余天数
		const surplus = 42 - (lastMonthDays.length + currentMonthDys.length)
		// 下个月开始几天
		const nextMonthDays = this._getNextMonthDays(surplus, dateObj)

		// 本月所有日期格子合并
		let days: Array<DateType> = []
		for (let i = 0; i < lastMonthDays.length; i++) {
			const item = lastMonthDays[i]
			days.push(item)
		}
		for (let i = 0; i < currentMonthDys.length; i++) {
			const item = currentMonthDys[i]
			days.push(item)
		}
		for (let i = 0; i < nextMonthDays.length; i++) {
			const item = nextMonthDays[i]
			days.push(item)
		}
		let weeks: Array<Array<DateType>> = []
		// 拼接数组  上个月开始几天 + 本月天数+ 下个月开始几天
		for (let i = 0; i < days.length; i += 7) {
			const item: Array<DateType> = days.slice(i, i + 7)
			weeks.push(item);
		}
		return weeks
	}

	/**
	 * 获取上月剩余天数
	 */
	_getLastMonthDays(firstDay: number, full: DateType): Array<DateType> {
		let dateArr: Array<DateType> = []
		for (let i = firstDay; i > 0; i--) {
			const month = full.month - 1
			const beforeDate = new Date(full.year, month, -i + 1).getDate()
			let nowDate = full.year + '-' + month + '-' + beforeDate

			let item: DateType = this.getDate(nowDate)
			item.disabled = true

			dateArr.push(item)
		}
		return dateArr
	}

	/**
	 * 获取本月天数
	 */
	_currentMonthDys(dateData: number, full: DateType): Array<DateType> {
		let dateArr: Array<DateType> = []
		for (let i = 1; i <= dateData; i++) {
			let nowDate = full.year + '-' + full.month + '-' + i
			let item: DateType = this.getDate(nowDate)
			item.disabled = false

			dateArr.push(item)
		}
		return dateArr
	}

	/**
	 * 获取下月天数
	 */
	_getNextMonthDays(surplus: number, full: DateType): Array<DateType> {
		let dateArr: Array<DateType> = []
		for (let i = 1; i < surplus + 1; i++) {
			const month = full.month + 1
			let nowDate = full.year + '-' + month + '-' + i
			let item: DateType = this.getDate(nowDate)
			item.disabled = true

			dateArr.push(item)
		}
		return dateArr
	}

	/**
	 * 计算阴历日期显示
	 */
	getlunar(year: number, month: number, date: number): LunarInfoType {
		return this.lunar.solar2lunar(year, month, date)
	}

	/**
	 * 获取任意时间
	 */
	getDate(date: string = '', AddDayCount: number = 0, str: string = 'day'): DateType {
		let dd: Date = new Date()
		if (date !== '') {
			const datePart = date.split(" ");
			const dateData = datePart[0].split("-");
			const year = parseInt(dateData[0])
			const month = parseInt(dateData[1])
			const day = parseInt(dateData[2])

			dd = new Date(year, month - 1, day)
		}

		switch (str) {
			case 'day':
				dd.setDate(dd.getDate() + AddDayCount);
				break;
			case 'month':
				dd.setMonth(dd.getMonth() + AddDayCount);
				break;
			case 'year':
				dd.setFullYear(dd.getFullYear() + AddDayCount);
				break;
		}

		const y = dd.getFullYear();
		const m = dd.getMonth() + 1;
		const d = dd.getDate();

		let nowDate = y + '-' + m + '-' + d
		const lunarData = this.getlunar(y, m, d)

		// 检查阳历节日
		const festivalKey = `${m}-${d}`
		const solarFestival = festival.get(festivalKey)

		// 检查农历节日
		const lunarFestivalKey = `${lunarData.lMonth}-${lunarData.lDay}`
		const lunarFestival = lfestival.get(lunarFestivalKey)

		// 农历月份中文
		const lMonthCn = this.lunar.toChinaMonth(lunarData.lMonth, lunarData.isLeap)
		// 农历日期中文
		const lDayCn = this.lunar.toChinaDay(lunarData.lDay)
		// 完整农历日期
		const lunarDate = lMonthCn + lDayCn

		const dataObj: DateType = {
			fullDate: nowDate,
			year: y,
			month: m,
			date: d,
			day: dd.getDay() + 1,
			lunar: lunarData.IDayCn,
			is_today: lunarData.isToday,
			disabled: false,
			data: lunarData,
			// 农历信息
			lYear: lunarData.lYear,
			lMonth: lunarData.lMonth,
			lDay: lunarData.lDay,
			lMonthCn: lMonthCn,
			lDayCn: lDayCn,
			lunarDate: lunarDate,
			// 节日信息
			festival: solarFestival?.title ?? null,
			lunarFestival: lunarFestival?.title ?? null,
			// 星期信息
			nWeek: lunarData.nWeek ?? 1,
			ncWeek: lunarData.ncWeek ?? "",
			// 节气信息
			isTerm: lunarData.isTerm ?? false,
			Term: lunarData.Term,
			// 其他信息
			isLeap: lunarData.isLeap,
			Animal: this.lunar.getAnimal(lunarData.lYear),
			astro: lunarData.astro ?? ""
		}

		return dataObj
	}

	/**
	 * 判断是否为当前月份
	 */
	isCurrentMonth(year: number, month: number): boolean {
		const today = new Date();
		return year == today.getFullYear() && month == today.getMonth() + 1;
	}

	/**
	 * 格式化年月显示 (xxxx/xx)
	 */
	formatYearMonth(year: number, month: number): string {
		const monthStr = month < 10 ? '0' + month : month.toString()
		return `${year}/${monthStr}`
	}

	/**
	 * 格式化日期为 YYYY-MM-DD
	 */
	formatDate(year: number, month: number, date: number): string {
		const monthStr = month < 10 ? '0' + month : month.toString()
		const dateStr = date < 10 ? '0' + date : date.toString()
		return `${year}-${monthStr}-${dateStr}`
	}
}
