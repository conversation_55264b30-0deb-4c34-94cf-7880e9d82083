{"version": 3, "file": "calendar.uts", "sourceRoot": "", "sources": ["utils/calendar.uts"], "names": [], "mappings": "AAAA,SAAS;AACT,MAAM,MAAM,aAAa,GAAG;IAC3B,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,EAAE,OAAO,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,MAAM,CAAA;CACd,CAAA;AAED,OAAO;AACP,MAAM,MAAM,QAAQ,GAAG;IACtB,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,GAAG,EAAE,MAAM,CAAC;IACZ,QAAQ,EAAE,OAAO,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,OAAO,CAAC;IAClB,IAAI,CAAC,EAAE,aAAa,CAAC;IACrB,OAAO;IACP,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC,CAAE,eAAe;IAClC,MAAM,EAAE,MAAM,CAAC,CAAI,eAAe;IAClC,SAAS,EAAE,MAAM,CAAC,CAAC,iBAAiB;IACpC,OAAO;IACP,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC,CAAM,OAAO;IACrC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC,OAAO;IACrC,OAAO;IACP,KAAK,EAAE,MAAM,CAAC,CAAK,kBAAkB;IACrC,MAAM,EAAE,MAAM,CAAC,CAAI,cAAc;IACjC,OAAO;IACP,MAAM,EAAE,OAAO,CAAC;IAChB,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB,OAAO;IACP,MAAM,EAAE,OAAO,CAAC,CAAG,OAAO;IAC1B,MAAM,EAAE,MAAM,CAAC,CAAI,KAAK;IACxB,KAAK,EAAE,MAAM,CAAC,CAAK,KAAK;CACxB,CAAA;AAED,WAAW;AACX,MAAM,MAAM,QAAQ,GAAG;IACtB,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,OAAO,CAAC;CAChB,CAAA;AAED,OAAO;AACP,KAAK,YAAY,GAAG;IACnB,KAAK,EAAE,MAAM,CAAC;CACd,CAAA;AAED;;;;GAIG;AACH,MAAM,UAAU,GAAG;IAClB,OAAO;IACP,YAAY;IACZ,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,YAAY;IACZ,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;CACxF,CAAA;AAED,6DAA6D;AAC7D,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;AAClJ,gDAAgD;AAChD,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;AAC9H,wBAAwB;AACxB,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;AAElE,OAAO;AACP,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;AAExI;;GAEG;AACH,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE;IAC9C,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,iBAAC;IAC1B,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC3B,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;CAC3B,CAAC,CAAA;AAEF;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE;IAC/C,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,iBAAC;IAC1B,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,iBAAC;IACxB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IACzB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC3B,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAC;IAC1B,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,iBAAC;IAC5B,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,iBAAC;CAC5B,CAAC,CAAA;AAEF;;GAEG;AACH,MAAM,SAAS,GAAG,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;IAChH,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;IAC9F,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;IAC9F,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC,CAAA;AAEhG;;GAEG;AACH,MAAM,SAAS,GAAG,CAAC,gCAAgC,EAAE,gCAAgC;IACpF,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,EAAE,gCAAgC,EAAE,gCAAgC;IACpG,gCAAgC,CAAC,CAAA;AAElC,SAAS;AACT,MAAM,OAAO,KAAK;IACjB,OAAO,CAAC,gBAAgB,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAA;IACpD,OAAO,CAAC,iBAAiB,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA;IAEvD,gBAAgB,CAAC;IAEjB;;;;;OAKG;IACH,YAAY,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG,MAAM;QACrD,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;IAED;;;;;OAKG;IACH,UAAU,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM;QAC5B,IAAI,CAAC,EAAE,MAAM,CAAA;QACb,QAAQ,CAAC,EAAE;YACV,KAAK,EAAE;gBACN,CAAC,GAAG,cAAc,CAAC;gBACnB,MAAK;YACN,KAAK,EAAE;gBACN,CAAC,GAAG,cAAc,CAAC;gBACnB,MAAK;YACN,KAAK,EAAE;gBACN,CAAC,GAAG,cAAc,CAAC;gBACnB,MAAK;YACN;gBACC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;gBAC/B,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;SACrB;QACD,OAAO,CAAC,CAAC,CAAC,CAAA;IACX,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;QAC9B,OAAO,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;QAC7B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC7B,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC1D;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAED,YAAY;IACZ,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;QACrC,IAAI,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,SAAS,IAAI,IAAI,EAAE;YACtB,OAAO,SAAS,CAAA;SAChB;QAED,SAAS,GAAG,EAAE,CAAC;QAEf,IAAI,SAAS,GAAG,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7B,IAAI,QAAQ,GAAG,CAAC,SAAS,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACrD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzB;QAED,OAAO;QACP,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,KAAK,GAAG,CAAC;YAAE,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QAE3C,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,SAAS;IACT,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;SACvC;QACD,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxC,GAAG,IAAI,IAAI,CAAC;QACb,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QACpC,OAAO,GAAG,CAAC;IACZ,CAAC;IAED;;;;;;;OAOG;IACH,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,aAAa;QAC1D,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAA;QAC1B,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC3B,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAA;QACzB,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAE3B,SAAS;QACT,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAElD,SAAS;QACT,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;QAC/B,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;QAE/C,SAAS;QACT,MAAM,gBAAgB,GAAG,GAAG,MAAM,IAAI,IAAI,EAAE,CAAA;QAC5C,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;QAErD,SAAS;QACT,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe;QAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe;QAC5D,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,IAAI,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;QAC/B,IAAI,SAAS,IAAI,CAAC,EAAE;YACnB,MAAM,GAAG,IAAI,CAAC;YACd,IAAI,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAC5B;QACD,IAAI,UAAU,IAAI,CAAC,EAAE;YACpB,MAAM,GAAG,IAAI,CAAC;YACd,IAAI,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAC5B;QAED,8BAA8B;QAC9B,IAAI,MAAM,EAAE,MAAM,CAAA;QAClB,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE;YAC3B,MAAM,GAAG,IAAI,CAAA;SACb;aAAM,IAAI,aAAa,IAAI,IAAI,EAAE;YACjC,MAAM,GAAG,aAAa,CAAC,KAAK,CAAA;SAC5B;aAAM,IAAI,aAAa,IAAI,IAAI,EAAE;YACjC,MAAM,GAAG,aAAa,CAAC,KAAK,CAAA;SAC5B;aAAM,IAAI,IAAI,IAAI,CAAC,EAAE;YACrB,MAAM,GAAG,QAAQ,CAAA;SACjB;aAAM;YACN,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;SAC9B;QAED,OAAO;QACP,IAAI,UAAU,GAAG,IAAI,IAAI,EAAE,CAAA;QAC3B,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,IAAI,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;YACjG,OAAO,GAAG,IAAI,CAAA;SACd;QAED,QAAQ;QACR,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QACrC,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,CAAA;QAC5B,IAAI,KAAK,IAAI,CAAC,EAAE;YACf,KAAK,GAAG,CAAC,CAAA,CAAE,QAAQ;SACnB;QACD,MAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;QACxF,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;QACzC,MAAM,MAAM,GAAG,cAAc,GAAG,KAAK,CAAA;QAErC,IAAI,IAAI,EAAE,aAAa,GAAG;YACzB,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,MAAM;YAChB,MAAM,EAAE,IAAI;YACZ,UAAU,EAAE,QAAQ;YACpB,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,CAAC;YACT,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE,MAAM;YAChB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;SAC3B,CAAA;QACD,OAAO,IAAI,CAAA;IACZ,CAAC;IAED,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,QAAQ;QACpD,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjC,uBAAuB;QACvB,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC;QAChH,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,IAAI,CAAC;SACf;QACD,IAAI,MAAM,GAAG,CAAC,EAAE;YACf,MAAM,IAAI,IAAI,CAAC;YACf,CAAC,EAAE,CAAC;SACJ;QAED,UAAU;QACV,IAAI,MAAM,EAAE,OAAO,GAAG,KAAK,CAAA;QAC3B,IAAI,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAE9B,IAAI,MAAM,GAAG,CAAC,EAAE;YACf,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACpD,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM,IAAI,IAAI,CAAC;aACf;YACD,IAAI,MAAM,IAAI,CAAC,EAAE;gBAChB,CAAC,EAAE,CAAC;aACJ;YACD,IAAI,MAAM,GAAG,CAAC,EAAE;gBACf,MAAM,IAAI,IAAI,CAAC;aACf;SACD;aAAM;YACN,mBAAmB;YACnB,IAAI,MAAM,IAAI,CAAC,EAAE,EAAE;gBAClB,IAAI,IAAI,EAAE,QAAQ,GAAG;oBACpB,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,EAAE;oBACV,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,KAAK;iBACb,CAAA;gBACD,IAAI,GAAG,IAAI,CAAA;aACX;SACD;QAED,QAAQ;QACR,IAAI,KAAK,GAAG,CAAC,EAAE;YACd,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;gBACnB,MAAM,GAAG,IAAI,CAAA;aACb;YACD,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;gBACnB,CAAC,EAAE,CAAA;aACH;SACD;QACD,MAAM,IAAI,EAAE,QAAQ,GAAG;YACtB,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,EAAE,MAAM;YAChB,MAAM,EAAE,MAAM;SACd,CAAA;QAED,OAAO,IAAI,CAAA;IACZ,CAAC;IAED;;;;;OAKG;IACH,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM;QACpC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE;YACzB,OAAO,CAAC,CAAC,CAAC;SACV;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YACpB,OAAO,CAAC,CAAC,CAAC;SACV;QACD,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QACnC,MAAM,KAAK,GAAG;YACb,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAE;YAClD,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAE;YACnD,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAE;YACpD,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAE;YACpD,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAE;YACpD,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAE;SACpD,CAAC;QACF,MAAM,OAAO,GAAG;YACf,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAExB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAExB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAExB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAExB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAExB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;SACxB,CAAC;QACF,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM;QAC3B,OAAO,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;IAC7B,CAAC;IAED;;;;;OAKG;IACH,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM;QAC5C,MAAM,CAAC,GAAG,8JAA8J,CAAC;QACzK,MAAM,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC7D,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;IACjI,CAAC;CACD;AAED,QAAQ;AACR,MAAM,OAAO,QAAQ;IACpB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAA;IAEpB;QACC,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAA;IACzB,CAAC;IAED,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG,QAAQ;QACvC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QACtE,MAAM,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAA;QAC9B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAA;QACjB,OAAO,IAAI,CAAA;IACZ,CAAC;IAED;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACtC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACzB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QAC3B,IAAI,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAA;QACpD,SAAS;QACT,IAAI,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA;QACnD,UAAU;QACV,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QAC/D,OAAO;QACP,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;QAClE,SAAS;QACT,MAAM,OAAO,GAAG,EAAE,GAAG,CAAC,aAAa,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAA;QACpE,UAAU;QACV,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAE9D,aAAa;QACb,IAAI,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAA;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACf;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,CAAA;YAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACf;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACf;QACD,IAAI,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAA;QACtC,gCAAgC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACxC,MAAM,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;YAClD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjB;QACD,OAAO,KAAK,CAAA;IACb,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QACnE,IAAI,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAA;QACjC,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YAC5B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA;YAC/D,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,UAAU,CAAA;YAExD,IAAI,IAAI,EAAE,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;YAEpB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAClB;QACD,OAAO,OAAO,CAAA;IACf,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAClE,IAAI,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAA;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,CAAA;YACpD,IAAI,IAAI,EAAE,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC1C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;YAErB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAClB;QACD,OAAO,OAAO,CAAA;IACf,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAClE,IAAI,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAA;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAA;YAC/C,IAAI,IAAI,EAAE,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;YAEpB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAClB;QACD,OAAO,OAAO,CAAA;IACf,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,aAAa;QACjE,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,EAAE,EAAE,WAAW,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,GAAG,KAAK,GAAG,QAAQ;QACjF,IAAI,EAAE,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;QACzB,IAAI,IAAI,KAAK,EAAE,EAAE;YAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAClC,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YACnC,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAEjC,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAA;SACnC;QAED,QAAQ,GAAG,EAAE;YACZ,KAAK,KAAK;gBACT,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,CAAC;gBACvC,MAAM;YACP,KAAK,OAAO;gBACX,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,CAAC;gBACzC,MAAM;YACP,KAAK,MAAM;gBACV,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC,CAAC;gBAC/C,MAAM;SACP;QAED,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3B,MAAM,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;QAEvB,IAAI,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;QAExC,SAAS;QACT,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;QAC/B,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;QAE/C,SAAS;QACT,MAAM,gBAAgB,GAAG,GAAG,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,EAAE,CAAA;QAChE,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;QAErD,SAAS;QACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAA;QAC5E,SAAS;QACT,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QACpD,SAAS;QACT,MAAM,SAAS,GAAG,QAAQ,GAAG,MAAM,CAAA;QAEnC,MAAM,OAAO,EAAE,QAAQ,GAAG;YACzB,QAAQ,EAAE,OAAO;YACjB,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC;YACpB,KAAK,EAAE,SAAS,CAAC,MAAM;YACvB,QAAQ,EAAE,SAAS,CAAC,OAAO;YAC3B,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,SAAS;YACf,OAAO;YACP,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,QAAQ,EAAE,QAAQ;YAClB,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,SAAS;YACpB,OAAO;YACP,QAAQ,EAAE,aAAa,EAAE,KAAK,IAAI,IAAI;YACtC,aAAa,EAAE,aAAa,EAAE,KAAK,IAAI,IAAI;YAC3C,OAAO;YACP,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI,CAAC;YAC3B,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,EAAE;YAC9B,OAAO;YACP,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,KAAK;YACjC,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO;YACP,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;YAC7C,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI,EAAE;SAC5B,CAAA;QAED,OAAO,OAAO,CAAA;IACf,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO;QACnD,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,OAAO,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM;QACnD,MAAM,QAAQ,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,IAAE,CAAA;QAC5D,OAAO,GAAG,IAAI,IAAI,QAAQ,EAAE,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM;QAC5D,MAAM,QAAQ,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,IAAE,CAAA;QAC5D,MAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAE,CAAA;QACxD,OAAO,GAAG,IAAI,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAA;IACxC,CAAC;CACD", "sourcesContent": ["// 农历信息类型\nexport type LunarInfoType = {\n\tlYear: number;\n\tlMonth: number;\n\tlDay: number;\n\tIMonthCn: string;\n\tIDayCn: string;\n\tcYear: number;\n\tcMonth: number;\n\tcDay: number;\n\tgzYear?: string;\n\tgzMonth?: string;\n\tgzDay?: string;\n\tisToday: boolean;\n\tisLeap: boolean;\n\tnWeek?: number;\n\tncWeek?: string;\n\tisTerm?: boolean;\n\tTerm?: string;\n\tastro?: string\n}\n\n// 日期类型\nexport type DateType = {\n\tfullDate: string;\n\tyear: number;\n\tmonth: number;\n\tdate: number;\n\tday: number;\n\tdisabled: boolean;\n\tlunar: string;\n\tis_today: boolean;\n\tdata?: LunarInfoType;\n\t// 农历信息\n\tlYear: number;\n\tlMonth: number;\n\tlDay: number;\n\tlMonthCn: string;  // 农历月份中文，如\"七月\"\n\tlDayCn: string;    // 农历日期中文，如\"初十\"\n\tlunarDate: string; // 完整农历日期，如\"七月初十\"\n\t// 节日信息\n\tfestival: string | null;      // 阳历节日\n\tlunarFestival: string | null; // 农历节日\n\t// 星期信息\n\tnWeek: number;     // 数字星期(1-7，周一到周日)\n\tncWeek: string;    // 中文星期，如\"星期一\"\n\t// 节气信息\n\tisTerm: boolean;\n\tTerm: string | null;\n\t// 其他信息\n\tisLeap: boolean;   // 是否闰月\n\tAnimal: string;    // 生肖\n\tastro: string;     // 星座\n}\n\n// 农历信息辅助类型\nexport type InfoType = {\n\tlunarY: number;\n\tlunarM: number;\n\tlunarD: number;\n\tisLeap: boolean;\n}\n\n// 节日类型\ntype FestivalType = {\n\ttitle: string;\n}\n\n/**\n * 农历1900-2100的润大小信息表\n * @Array Of Property\n * @return Hex\n */\nconst lunarYears = [\n\t0x04bd8,\n\t// 1901-2000\n\t0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2, 0x04ae0,\n\t0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977, 0x04970,\n\t0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970, 0x06566,\n\t0x0d4a0, 0x0ea50, 0x16a95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950, 0x0d4a0,\n\t0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557, 0x06ca0,\n\t0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0, 0x0aea6,\n\t0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0, 0x096d0,\n\t0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6, 0x095b0,\n\t0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570, 0x04af5,\n\t0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x05ac0, 0x0ab60, 0x096d5, 0x092e0, 0x0c960,\n\t// 2001-2100\n\t0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5, 0x0a950,\n\t0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930, 0x07954,\n\t0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530, 0x05aa0,\n\t0x076a3, 0x096d0, 0x04afb, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45, 0x0b5a0,\n\t0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0, 0x14b63,\n\t0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0, 0x092e0,\n\t0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4, 0x052d0,\n\t0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0, 0x0b273,\n\t0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160, 0x0e968,\n\t0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252, 0x0d520\n]\n\n// ['月','正','一','二','三','四','五','六','七','八','九','十','冬','腊'];\nconst N_STR_3 = [\"\\u6708\", \"\\u6b63\", \"\\u4e8c\", \"\\u4e09\", \"\\u56db\", \"\\u4e94\", \"\\u516d\", \"\\u4e03\", \"\\u516b\", \"\\u4e5d\", \"\\u5341\", \"\\u51ac\", \"\\u814a\"]\n// ['日','一','二','三','四','五','六','七','八','九','十']\nconst N_STR_1 = [\"\\u65e5\", \"\\u4e00\", \"\\u4e8c\", \"\\u4e09\", \"\\u56db\", \"\\u4e94\", \"\\u516d\", \"\\u4e03\", \"\\u516b\", \"\\u4e5d\", \"\\u5341\"]\n// ['初','十','廿','卅','闰']\nconst N_STR_2 = [\"\\u521d\", \"\\u5341\", \"\\u5eff\", \"\\u5345\", \"\\u95f0\"]\n\n// 生肖数组\nconst Animals = [\"\\u9f20\", \"\\u725b\", \"\\u864e\", \"\\u5154\", \"\\u9f99\", \"\\u86c7\", \"\\u9a6c\", \"\\u7f8a\", \"\\u7334\", \"\\u9e21\", \"\\u72d7\", \"\\u732a\"]\n\n/**\n * 阳历节日\n */\nconst festival = new Map<string, FestivalType>([\n\t['1-1', { title: '元旦节1' }],\n\t['2-14', { title: '情人节' }],\n\t['3-8', { title: '妇女节' }],\n\t['3-12', { title: '植树节' }],\n\t['4-1', { title: '愚人节' }],\n\t['4-4', { title: '清明节' }],\n\t['5-1', { title: '劳动节' }],\n\t['5-4', { title: '青年节' }],\n\t['5-12', { title: '护士节' }],\n\t['6-1', { title: '儿童节' }],\n\t['7-1', { title: '建党节' }],\n\t['8-1', { title: '建军节' }],\n\t['9-10', { title: '教师节' }],\n\t['10-1', { title: '国庆节' }],\n\t['12-24', { title: '平安夜' }],\n\t['12-25', { title: '圣诞节' }]\n])\n\n/**\n * 农历节日\n */\nconst lfestival = new Map<string, FestivalType>([\n\t['12-30', { title: '除夕' }],\n\t['1-1', { title: '春节' }],\n\t['1-15', { title: '元宵节' }],\n\t['2-2', { title: '龙抬头' }],\n\t['5-5', { title: '端午节' }],\n\t['7-7', { title: '七夕节' }],\n\t['7-15', { title: '中元节' }],\n\t['8-15', { title: '中秋节' }],\n\t['9-9', { title: '重阳节' }],\n\t['10-1', { title: '寒衣节' }],\n\t['10-15', { title: '下元节' }],\n\t['12-8', { title: '腊八节' }],\n\t['12-23', { title: '北方小年' }],\n\t['12-24', { title: '南方小年' }]\n])\n\n/**\n * 24节气速查表\n */\nconst solarTerm = [\"\\u5c0f\\u5bd2\", \"\\u5927\\u5bd2\", \"\\u7acb\\u6625\", \"\\u96e8\\u6c34\", \"\\u60ca\\u86f0\", \"\\u6625\\u5206\",\n\t\"\\u6e05\\u660e\", \"\\u8c37\\u96e8\", \"\\u7acb\\u590f\", \"\\u5c0f\\u6ee1\", \"\\u8292\\u79cd\", \"\\u590f\\u81f3\",\n\t\"\\u5c0f\\u6691\", \"\\u5927\\u6691\", \"\\u7acb\\u79cb\", \"\\u5904\\u6691\", \"\\u767d\\u9732\", \"\\u79cb\\u5206\",\n\t\"\\u5bd2\\u9732\", \"\\u971c\\u964d\", \"\\u7acb\\u51ac\", \"\\u5c0f\\u96ea\", \"\\u5927\\u96ea\", \"\\u51ac\\u81f3\"]\n\n/**\n * 1900-2100各年的24节气日期速查表\n */\nconst sTermInfo = ['9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\n\t'97bcf97c3598082c95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f',\n\t'b027097bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',\n\t'97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa', '9778397bd19801ec9210c965cc920e',\n\t'97b6b97bd19801ec95f8c965cc920f', '97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2',\n\t'9778397bd197c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e', '97bd09801d98082c95f8e1cfcc920f',\n\t'97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e',\n\t'97bcf97c3598082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f',\n\t'97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\n\t'97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',\n\t'97bd097bd07f595b0b6fc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '9778397bd19801ec9210c9274c920e',\n\t'97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',\n\t'9778397bd097c36c9210c9274c920e', '97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722',\n\t'7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e',\n\t'97bd07f1487f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722',\n\t'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\n\t'97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722',\n\t'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c91aa', '97b6b97bd197c36c9210c9274c920e',\n\t'97bcf7f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2',\n\t'9778397bd097c36c9210c9274c920e', '97b6b7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722',\n\t'7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b70c9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',\n\t'7f0e37f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\n\t'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',\n\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\n\t'97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\n\t'9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722',\n\t'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c91aa', '97b6b7f0e47f149b0723b0787b0721',\n\t'7f0e27f0e47f531b0723b0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2',\n\t'977837f0e37f149b0723b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722',\n\t'7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0721',\n\t'7f0e37f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\n\t'977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\n\t'7f0e397bd097c35b0b6fc920fb0722', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\n\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\n\t'977837f0e37f14998082b0787b06bd', '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722',\n\t'7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0723b06bd', '7f07e7f0e37f149b0723b0787b0721',\n\t'7f0e27f0e47f531b0723b0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14898082b0723b02d5',\n\t'7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f595b0b0bb0b6fb0722',\n\t'7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722',\n\t'7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35',\n\t'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\n\t'7f0e37f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\n\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f149b0723b0787b0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35',\n\t'7ec967f0e37f14998082b0723b06bd', '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',\n\t'7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0723b06bd', '7f07e7f0e37f14998083b0787b0721',\n\t'7f0e27f0e47f531b0723b0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14898082b0723b02d5',\n\t'7f07e7f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66aa89801e9808297c35',\n\t'665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722',\n\t'7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd',\n\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b072297c35',\n\t'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e26665b66a449801e9808297c35',\n\t'665f67f0e37f1489801eb072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\n\t'7f0e27f1487f531b0b0bb0b6fb0722']\n\n// 完整的农历类\nexport class Lunar {\n\tprivate lunarYearDaysMap = new Map<number, number>()\n\tprivate lunarMonthDaysMap = new Map<number, number[]>()\n\n\tconstructor() { }\n\n\t/**\n\t * 传入农历数字月份返回汉语通俗表示法\n\t * @param lunar month\n\t * @return Cn string\n\t * @eg:let cnMonth = calendar.toChinaMonth(12) ;//cnMonth='腊月'\n\t */\n\ttoChinaMonth(m: number, leap: boolean = false): string { // 月 => \\u6708\n\t\treturn leap ? (N_STR_3[4] + N_STR_3[m] + N_STR_3[0]) : (N_STR_3[m] + N_STR_3[0]);\n\t}\n\n\t/**\n\t * 传入农历日期数字返回汉字表示法\n\t * @param lunar day\n\t * @return Cn string\n\t * @eg:let cnDay = calendar.toChinaDay(21) ;//cnMonth='廿一'\n\t */\n\ttoChinaDay(d: number): string { // 日 => \\u65e5\n\t\tlet s: string\n\t\tswitch (d) {\n\t\t\tcase 10:\n\t\t\t\ts = '\\u521d\\u5341';\n\t\t\t\tbreak\n\t\t\tcase 20:\n\t\t\t\ts = '\\u4e8c\\u5341';\n\t\t\t\tbreak\n\t\t\tcase 30:\n\t\t\t\ts = '\\u4e09\\u5341';\n\t\t\t\tbreak\n\t\t\tdefault:\n\t\t\t\ts = N_STR_2[Math.floor(d / 10)]\n\t\t\t\ts += N_STR_1[d % 10]\n\t\t}\n\t\treturn (s)\n\t}\n\n\t/**\n\t * 返回农历y年闰月是哪个月；若y年没有闰月 则返回0\n\t * @param lunar Year\n\t * @return Number (0-12)\n\t * @eg:let leapMonth = calendar.leapMonth(1987) ;//leapMonth=6\n\t */\n\tleapMonth(year: number): number {\n\t\treturn lunarYears[year - 1900] & 0xF;\n\t}\n\n\t/**\n\t * 返回农历y年闰月的天数 若该年没有闰月则返回0\n\t * @param lunar Year\n\t * @return Number (0、29、30)\n\t * @eg:let leapMonthDay = calendar.leapDays(1987) ;//leapMonthDay=29\n\t */\n\tleapDays(year: number): number {\n\t\tif (this.leapMonth(year) > 0) {\n\t\t\treturn (lunarYears[year - 1900] & 0x10000) != 0 ? 30 : 29;\n\t\t}\n\t\treturn 0;\n\t}\n\n\t// 某年份农历各月天数\n\tlunarMonthDays(year: number): number[] {\n\t\tlet monthDays = this.lunarMonthDaysMap.get(year)\n\t\tif (monthDays != null) {\n\t\t\treturn monthDays\n\t\t}\n\n\t\tmonthDays = [];\n\n\t\tlet lunarYear = lunarYears[year - 1900];\n\n\t\tfor (let i = 15; i >= 4; i--) {\n\t\t\tlet monthDay = (lunarYear >> i & 0x1) != 0 ? 30 : 29;\n\t\t\tmonthDays.push(monthDay);\n\t\t}\n\n\t\t// 添加闰月\n\t\tlet leapM = this.leapMonth(year);\n\n\t\tif (leapM > 0) monthDays.splice(leapM, 0, this.leapDays(year));\n\t\tthis.lunarMonthDaysMap.set(year, monthDays)\n\n\t\treturn monthDays;\n\t}\n\n\t// 某年农历天数\n\tlunarYearDays(year: number): number {\n\t\tif (this.lunarYearDaysMap.has(year)) {\n\t\t\treturn this.lunarYearDaysMap.get(year)!\n\t\t}\n\t\tlet num = 0;\n\t\tthis.lunarMonthDays(year).forEach(item => {\n\t\t\tnum += item;\n\t\t});\n\t\tthis.lunarYearDaysMap.set(year, num)\n\t\treturn num;\n\t}\n\n\t/**\n\t * 传入阳历年月日获得详细的公历、农历object信息 <=>JSON\n\t * @param y  solar year\n\t * @param m  solar month\n\t * @param d  solar day\n\t * @return JSON object\n\t * @eg:__f__('log','at utils/calendar.uts:341',calendar.solar2lunar(1987,11,01));\n\t */\n\tsolar2lunar(y: number, m: number, d: number): LunarInfoType { // 参数区间1900.1.31~2100.12.31\n\t\tlet moonDay = this.solar_date(y, m, d);\n\t\tlet lYear = moonDay.lunarY\n\t\tlet lMonth = moonDay.lunarM\n\t\tlet lDay = moonDay.lunarD\n\t\tlet isLeap = moonDay.isLeap\n\n\t\t// 计算农历日期\n\t\tconst IMonthCn = this.toChinaMonth(lMonth, isLeap)\n\n\t\t// 检查阳历节日\n\t\tconst festivalKey = `${m}-${d}`\n\t\tconst solarFestival = festival.get(festivalKey)\n\n\t\t// 检查农历节日\n\t\tconst lunarFestivalKey = `${lMonth}-${lDay}`\n\t\tconst lunarFestival = lfestival.get(lunarFestivalKey)\n\n\t\t// 检查24节气\n\t\tconst firstNode = this.getTerm(y, (m * 2 - 1)); // 返回当月「节」为几日开始\n\t\tconst secondNode = this.getTerm(y, (m * 2)); // 返回当月「气」为几日开始\n\t\tlet isTerm = false;\n\t\tlet Term: string | null = null;\n\t\tif (firstNode == d) {\n\t\t\tisTerm = true;\n\t\t\tTerm = solarTerm[m * 2 - 2];\n\t\t}\n\t\tif (secondNode == d) {\n\t\t\tisTerm = true;\n\t\t\tTerm = solarTerm[m * 2 - 1];\n\t\t}\n\n\t\t// 优先级：节气 > 阳历节日 > 农历节日 > 农历日期\n\t\tlet IDayCn: string\n\t\tif (isTerm && Term != null) {\n\t\t\tIDayCn = Term\n\t\t} else if (solarFestival != null) {\n\t\t\tIDayCn = solarFestival.title\n\t\t} else if (lunarFestival != null) {\n\t\t\tIDayCn = lunarFestival.title\n\t\t} else if (lDay == 1) {\n\t\t\tIDayCn = IMonthCn\n\t\t} else {\n\t\t\tIDayCn = this.toChinaDay(lDay)\n\t\t}\n\n\t\t// 是否今天\n\t\tlet isTodayObj = new Date()\n\t\tlet isToday = false\n\t\tif (isTodayObj.getFullYear() == y && isTodayObj.getMonth() + 1 == m && isTodayObj.getDate() == d) {\n\t\t\tisToday = true\n\t\t}\n\n\t\t// 星期几计算\n\t\tconst dateObj = new Date(y, m - 1, d)\n\t\tlet nWeek = dateObj.getDay()\n\t\tif (nWeek == 0) {\n\t\t\tnWeek = 7  // 周日改为7\n\t\t}\n\t\tconst weekNames = [\"\\u65e5\", \"\\u4e00\", \"\\u4e8c\", \"\\u4e09\", \"\\u56db\", \"\\u4e94\", \"\\u516d\"]\n\t\tconst cWeek = weekNames[dateObj.getDay()]\n\t\tconst ncWeek = \"\\u661f\\u671f\" + cWeek\n\n\t\tlet info: LunarInfoType = {\n\t\t\t'lYear': lYear,\n\t\t\t'lMonth': lMonth,\n\t\t\t'lDay': lDay,\n\t\t\t'IMonthCn': IMonthCn,\n\t\t\t'IDayCn': IDayCn,\n\t\t\t'cYear': y,\n\t\t\t'cMonth': m,\n\t\t\t'cDay': d,\n\t\t\t'isToday': isToday,\n\t\t\t'isLeap': isLeap,\n\t\t\t'isTerm': isTerm,\n\t\t\t'Term': Term,\n\t\t\t'nWeek': nWeek,\n\t\t\t'ncWeek': ncWeek,\n\t\t\t'astro': this.toAstro(m, d)\n\t\t}\n\t\treturn info\n\t}\n\n\tsolar_date(y: number, m: number, d: number): InfoType { // 参数区间1900.1.31~2100.12.31\n\t\tlet date = new Date(y, m - 1, d);\n\n\t\t// 参照日期 1901-02-19 正月初一\n\t\tlet offset = (Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()) - Date.UTC(1901, 1, 19)) / 86400000;\n\t\tlet temp = 0\n\t\tlet i: number;\n\t\tfor (i = 1901; i < 2101 && offset > 0; i++) {\n\t\t\ttemp = this.lunarYearDays(i);\n\t\t\toffset -= temp;\n\t\t}\n\t\tif (offset < 0) {\n\t\t\toffset += temp;\n\t\t\ti--;\n\t\t}\n\n\t\t// 农历年、月、日\n\t\tlet isLeap: boolean = false\n\t\tlet j: number = 0;\n\t\tlet monthDays = this.lunarMonthDays(i);\n\t\tlet leapM = this.leapMonth(i);\n\n\t\tif (offset > 0) {\n\t\t\tfor (j = 0; j < monthDays.length && offset > 0; j++) {\n\t\t\t\ttemp = monthDays[j];\n\t\t\t\toffset -= temp;\n\t\t\t}\n\t\t\tif (offset == 0) {\n\t\t\t\tj++;\n\t\t\t}\n\t\t\tif (offset < 0) {\n\t\t\t\toffset += temp;\n\t\t\t}\n\t\t} else {\n\t\t\t// 补偿公历1901年2月的农历信息\n\t\t\tif (offset == -23) {\n\t\t\t\tlet info: InfoType = {\n\t\t\t\t\tlunarY: i,\n\t\t\t\t\tlunarM: 12,\n\t\t\t\t\tlunarD: 8,\n\t\t\t\t\tisLeap: false\n\t\t\t\t}\n\t\t\t\tinfo = info\n\t\t\t}\n\t\t}\n\n\t\t// 矫正闰年月\n\t\tif (leapM > 0) {\n\t\t\tif (j == leapM + 1) {\n\t\t\t\tisLeap = true\n\t\t\t}\n\t\t\tif (j >= leapM + 1) {\n\t\t\t\tj--\n\t\t\t}\n\t\t}\n\t\tconst info: InfoType = {\n\t\t\tlunarY: i,\n\t\t\tlunarM: j,\n\t\t\tlunarD: ++offset,\n\t\t\tisLeap: isLeap\n\t\t}\n\n\t\treturn info\n\t}\n\n\t/**\n\t * 传入公历年获得该年第n个节气的公历日期\n\t * @param y公历年(1900-2100)；n二十四节气中的第几个节气(1~24)；从n=1(小寒)算起\n\t * @return day Number\n\t * @eg:var _24 = calendar.getTerm(1987,3) ;//_24=4;意即1987年2月4日立春\n\t */\n\tgetTerm(y: number, n: number): number {\n\t\tif (y < 1900 || y > 2100) {\n\t\t\treturn -1;\n\t\t}\n\t\tif (n < 1 || n > 24) {\n\t\t\treturn -1;\n\t\t}\n\t\tconst _table = sTermInfo[y - 1900];\n\t\tconst _info = [\n\t\t\tparseInt('0x' + _table.substring(0, 5)).toString(),\n\t\t\tparseInt('0x' + _table.substring(5, 10)).toString(),\n\t\t\tparseInt('0x' + _table.substring(10, 15)).toString(),\n\t\t\tparseInt('0x' + _table.substring(15, 20)).toString(),\n\t\t\tparseInt('0x' + _table.substring(20, 25)).toString(),\n\t\t\tparseInt('0x' + _table.substring(25, 30)).toString()\n\t\t];\n\t\tconst _calday = [\n\t\t\t_info[0].substring(0, 1),\n\t\t\t_info[0].substring(1, 3),\n\t\t\t_info[0].substring(3, 4),\n\t\t\t_info[0].substring(4, 6),\n\n\t\t\t_info[1].substring(0, 1),\n\t\t\t_info[1].substring(1, 3),\n\t\t\t_info[1].substring(3, 4),\n\t\t\t_info[1].substring(4, 6),\n\n\t\t\t_info[2].substring(0, 1),\n\t\t\t_info[2].substring(1, 3),\n\t\t\t_info[2].substring(3, 4),\n\t\t\t_info[2].substring(4, 6),\n\n\t\t\t_info[3].substring(0, 1),\n\t\t\t_info[3].substring(1, 3),\n\t\t\t_info[3].substring(3, 4),\n\t\t\t_info[3].substring(4, 6),\n\n\t\t\t_info[4].substring(0, 1),\n\t\t\t_info[4].substring(1, 3),\n\t\t\t_info[4].substring(3, 4),\n\t\t\t_info[4].substring(4, 6),\n\n\t\t\t_info[5].substring(0, 1),\n\t\t\t_info[5].substring(1, 3),\n\t\t\t_info[5].substring(3, 4),\n\t\t\t_info[5].substring(4, 6),\n\t\t];\n\t\treturn parseInt(_calday[n - 1]);\n\t}\n\n\t/**\n\t * 年份转生肖[!仅能大致转换] => 精确划分生肖分界线是\"立春\"\n\t * @param y year\n\t * @return Cn string\n\t */\n\tgetAnimal(y: number): string {\n\t\treturn Animals[(y - 4) % 12]\n\t}\n\n\t/**\n\t * 公历月、日判断所属星座\n\t * @param cMonth\n\t * @param cDay\n\t * @return Cn string\n\t */\n\ttoAstro(cMonth: number, cDay: number): string {\n\t\tconst s = \"\\u9b54\\u7faf\\u6c34\\u74f6\\u53cc\\u9c7c\\u767d\\u7f8a\\u91d1\\u725b\\u53cc\\u5b50\\u5de8\\u87f9\\u72ee\\u5b50\\u5904\\u5973\\u5929\\u79e4\\u5929\\u874e\\u5c04\\u624b\\u9b54\\u7faf\";\n\t\tconst arr = [20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22];\n\t\treturn s.substring(cMonth * 2 - (cDay < arr[cMonth - 1] ? 2 : 0), cMonth * 2 - (cDay < arr[cMonth - 1] ? 2 : 0) + 2) + \"\\u5ea7\";\n\t}\n}\n\n// 日历工具类\nexport class Calendar {\n\tprivate lunar: Lunar\n\t\n\tconstructor() {\n\t\tthis.lunar = new Lunar()\n\t}\n\n\tgetDateInfo(time: string = ''): DateType {\n\t\tconst nowDate = this.getDate(time)\n\t\tconst lunar = this.getlunar(nowDate.year, nowDate.month, nowDate.date)\n\t\tconst item: DateType = nowDate\n\t\titem.data = lunar\n\t\treturn item\n\t}\n\n\t/**\n\t * 获取每周数据\n\t * @param {string} dateData\n\t */\n\tgetWeeks(dateData: string = ''): Array<Array<DateType>> {\n\t\tconst dateObj = this.getDate(dateData)\n\t\tconst year = dateObj.year\n\t\tconst month = dateObj.month\n\t\tlet firstDay = new Date(year, month - 1, 0).getDay()\n\t\t// 获取本月天数\n\t\tlet currentDay = new Date(year, month, 0).getDate()\n\t\t// 上个月末尾几天\n\t\tconst lastMonthDays = this._getLastMonthDays(firstDay, dateObj)\n\t\t// 本月天数\n\t\tconst currentMonthDys = this._currentMonthDys(currentDay, dateObj)\n\t\t// 本月剩余天数\n\t\tconst surplus = 42 - (lastMonthDays.length + currentMonthDys.length)\n\t\t// 下个月开始几天\n\t\tconst nextMonthDays = this._getNextMonthDays(surplus, dateObj)\n\n\t\t// 本月所有日期格子合并\n\t\tlet days: Array<DateType> = []\n\t\tfor (let i = 0; i < lastMonthDays.length; i++) {\n\t\t\tconst item = lastMonthDays[i]\n\t\t\tdays.push(item)\n\t\t}\n\t\tfor (let i = 0; i < currentMonthDys.length; i++) {\n\t\t\tconst item = currentMonthDys[i]\n\t\t\tdays.push(item)\n\t\t}\n\t\tfor (let i = 0; i < nextMonthDays.length; i++) {\n\t\t\tconst item = nextMonthDays[i]\n\t\t\tdays.push(item)\n\t\t}\n\t\tlet weeks: Array<Array<DateType>> = []\n\t\t// 拼接数组  上个月开始几天 + 本月天数+ 下个月开始几天\n\t\tfor (let i = 0; i < days.length; i += 7) {\n\t\t\tconst item: Array<DateType> = days.slice(i, i + 7)\n\t\t\tweeks.push(item);\n\t\t}\n\t\treturn weeks\n\t}\n\n\t/**\n\t * 获取上月剩余天数\n\t */\n\t_getLastMonthDays(firstDay: number, full: DateType): Array<DateType> {\n\t\tlet dateArr: Array<DateType> = []\n\t\tfor (let i = firstDay; i > 0; i--) {\n\t\t\tconst month = full.month - 1\n\t\t\tconst beforeDate = new Date(full.year, month, -i + 1).getDate()\n\t\t\tlet nowDate = full.year + '-' + month + '-' + beforeDate\n\n\t\t\tlet item: DateType = this.getDate(nowDate)\n\t\t\titem.disabled = true\n\n\t\t\tdateArr.push(item)\n\t\t}\n\t\treturn dateArr\n\t}\n\n\t/**\n\t * 获取本月天数\n\t */\n\t_currentMonthDys(dateData: number, full: DateType): Array<DateType> {\n\t\tlet dateArr: Array<DateType> = []\n\t\tfor (let i = 1; i <= dateData; i++) {\n\t\t\tlet nowDate = full.year + '-' + full.month + '-' + i\n\t\t\tlet item: DateType = this.getDate(nowDate)\n\t\t\titem.disabled = false\n\n\t\t\tdateArr.push(item)\n\t\t}\n\t\treturn dateArr\n\t}\n\n\t/**\n\t * 获取下月天数\n\t */\n\t_getNextMonthDays(surplus: number, full: DateType): Array<DateType> {\n\t\tlet dateArr: Array<DateType> = []\n\t\tfor (let i = 1; i < surplus + 1; i++) {\n\t\t\tconst month = full.month + 1\n\t\t\tlet nowDate = full.year + '-' + month + '-' + i\n\t\t\tlet item: DateType = this.getDate(nowDate)\n\t\t\titem.disabled = true\n\n\t\t\tdateArr.push(item)\n\t\t}\n\t\treturn dateArr\n\t}\n\n\t/**\n\t * 计算阴历日期显示\n\t */\n\tgetlunar(year: number, month: number, date: number): LunarInfoType {\n\t\treturn this.lunar.solar2lunar(year, month, date)\n\t}\n\n\t/**\n\t * 获取任意时间\n\t */\n\tgetDate(date: string = '', AddDayCount: number = 0, str: string = 'day'): DateType {\n\t\tlet dd: Date = new Date()\n\t\tif (date !== '') {\n\t\t\tconst datePart = date.split(\" \");\n\t\t\tconst dateData = datePart[0].split(\"-\");\n\t\t\tconst year = parseInt(dateData[0])\n\t\t\tconst month = parseInt(dateData[1])\n\t\t\tconst day = parseInt(dateData[2])\n\n\t\t\tdd = new Date(year, month - 1, day)\n\t\t}\n\n\t\tswitch (str) {\n\t\t\tcase 'day':\n\t\t\t\tdd.setDate(dd.getDate() + AddDayCount);\n\t\t\t\tbreak;\n\t\t\tcase 'month':\n\t\t\t\tdd.setMonth(dd.getMonth() + AddDayCount);\n\t\t\t\tbreak;\n\t\t\tcase 'year':\n\t\t\t\tdd.setFullYear(dd.getFullYear() + AddDayCount);\n\t\t\t\tbreak;\n\t\t}\n\n\t\tconst y = dd.getFullYear();\n\t\tconst m = dd.getMonth() + 1;\n\t\tconst d = dd.getDate();\n\n\t\tlet nowDate = y + '-' + m + '-' + d\n\t\tconst lunarData = this.getlunar(y, m, d)\n\n\t\t// 检查阳历节日\n\t\tconst festivalKey = `${m}-${d}`\n\t\tconst solarFestival = festival.get(festivalKey)\n\n\t\t// 检查农历节日\n\t\tconst lunarFestivalKey = `${lunarData.lMonth}-${lunarData.lDay}`\n\t\tconst lunarFestival = lfestival.get(lunarFestivalKey)\n\n\t\t// 农历月份中文\n\t\tconst lMonthCn = this.lunar.toChinaMonth(lunarData.lMonth, lunarData.isLeap)\n\t\t// 农历日期中文\n\t\tconst lDayCn = this.lunar.toChinaDay(lunarData.lDay)\n\t\t// 完整农历日期\n\t\tconst lunarDate = lMonthCn + lDayCn\n\n\t\tconst dataObj: DateType = {\n\t\t\tfullDate: nowDate,\n\t\t\tyear: y,\n\t\t\tmonth: m,\n\t\t\tdate: d,\n\t\t\tday: dd.getDay() + 1,\n\t\t\tlunar: lunarData.IDayCn,\n\t\t\tis_today: lunarData.isToday,\n\t\t\tdisabled: false,\n\t\t\tdata: lunarData,\n\t\t\t// 农历信息\n\t\t\tlYear: lunarData.lYear,\n\t\t\tlMonth: lunarData.lMonth,\n\t\t\tlDay: lunarData.lDay,\n\t\t\tlMonthCn: lMonthCn,\n\t\t\tlDayCn: lDayCn,\n\t\t\tlunarDate: lunarDate,\n\t\t\t// 节日信息\n\t\t\tfestival: solarFestival?.title ?? null,\n\t\t\tlunarFestival: lunarFestival?.title ?? null,\n\t\t\t// 星期信息\n\t\t\tnWeek: lunarData.nWeek ?? 1,\n\t\t\tncWeek: lunarData.ncWeek ?? \"\",\n\t\t\t// 节气信息\n\t\t\tisTerm: lunarData.isTerm ?? false,\n\t\t\tTerm: lunarData.Term,\n\t\t\t// 其他信息\n\t\t\tisLeap: lunarData.isLeap,\n\t\t\tAnimal: this.lunar.getAnimal(lunarData.lYear),\n\t\t\tastro: lunarData.astro ?? \"\"\n\t\t}\n\n\t\treturn dataObj\n\t}\n\n\t/**\n\t * 判断是否为当前月份\n\t */\n\tisCurrentMonth(year: number, month: number): boolean {\n\t\tconst today = new Date();\n\t\treturn year == today.getFullYear() && month == today.getMonth() + 1;\n\t}\n\n\t/**\n\t * 格式化年月显示 (xxxx/xx)\n\t */\n\tformatYearMonth(year: number, month: number): string {\n\t\tconst monthStr = month < 10 ? '0' + month : month.toString()\n\t\treturn `${year}/${monthStr}`\n\t}\n\n\t/**\n\t * 格式化日期为 YYYY-MM-DD\n\t */\n\tformatDate(year: number, month: number, date: number): string {\n\t\tconst monthStr = month < 10 ? '0' + month : month.toString()\n\t\tconst dateStr = date < 10 ? '0' + date : date.toString()\n\t\treturn `${year}-${monthStr}-${dateStr}`\n\t}\n}\n"]}