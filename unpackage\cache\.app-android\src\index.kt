@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.connectSocket as uni_connectSocket
import io.dcloud.uniapp.extapi.exit as uni_exit
import io.dcloud.uniapp.extapi.showToast as uni_showToast
val runBlock1 = run {
    __uniConfig.getAppStyles = fun(): Map<String, Map<String, Map<String, Any>>> {
        return GenApp.styles
    }
}
fun initRuntimeSocket(hosts: String, port: String, id: String): UTSPromise<SocketTask?> {
    if (hosts == "" || port == "" || id == "") {
        return UTSPromise.resolve(null)
    }
    return hosts.split(",").reduce<UTSPromise<SocketTask?>>(fun(promise: UTSPromise<SocketTask?>, host: String): UTSPromise<SocketTask?> {
        return promise.then(fun(socket): UTSPromise<SocketTask?> {
            if (socket != null) {
                return UTSPromise.resolve(socket)
            }
            return tryConnectSocket(host, port, id)
        }
        )
    }
    , UTSPromise.resolve(null))
}
val SOCKET_TIMEOUT: Number = 500
fun tryConnectSocket(host: String, port: String, id: String): UTSPromise<SocketTask?> {
    return UTSPromise(fun(resolve, reject){
        val socket = uni_connectSocket(ConnectSocketOptions(url = "ws://" + host + ":" + port + "/" + id, fail = fun(_) {
            resolve(null)
        }
        ))
        val timer = setTimeout(fun(){
            socket.close(CloseSocketOptions(code = 1006, reason = "connect timeout"))
            resolve(null)
        }
        , SOCKET_TIMEOUT)
        socket.onOpen(fun(e){
            clearTimeout(timer)
            resolve(socket)
        }
        )
        socket.onClose(fun(e){
            clearTimeout(timer)
            resolve(null)
        }
        )
        socket.onError(fun(e){
            clearTimeout(timer)
            resolve(null)
        }
        )
    }
    )
}
fun initRuntimeSocketService(): UTSPromise<Boolean> {
    val hosts: String = "************,*************,************,127.0.0.1"
    val port: String = "8090"
    val id: String = "app-android_5fm1FD"
    if (hosts == "" || port == "" || id == "") {
        return UTSPromise.resolve(false)
    }
    var socketTask: SocketTask? = null
    __registerWebViewUniConsole(fun(): String {
        return "!function(){\"use strict\";\"function\"==typeof SuppressedError&&SuppressedError;var e=[\"log\",\"warn\",\"error\",\"info\",\"debug\"],n=e.reduce((function(e,n){return e[n]=console[n].bind(console),e}),{}),t=null,r=new Set,o={};function i(e){if(null!=t){var n=e.map((function(e){if(\"string\"==typeof e)return e;var n=e&&\"promise\"in e&&\"reason\"in e,t=n?\"UnhandledPromiseRejection: \":\"\";if(n&&(e=e.reason),e instanceof Error&&e.stack)return e.message&&!e.stack.includes(e.message)?\"\".concat(t).concat(e.message,\"\\n\").concat(e.stack):\"\".concat(t).concat(e.stack);if(\"object\"==typeof e&&null!==e)try{return t+JSON.stringify(e)}catch(e){return t+String(e)}return t+String(e)})).filter(Boolean);n.length>0&&t(JSON.stringify(Object.assign({type:\"error\",data:n},o)))}else e.forEach((function(e){r.add(e)}))}function a(e,n){try{return{type:e,args:u(n)}}catch(e){}return{type:e,args:[]}}function u(e){return e.map((function(e){return c(e)}))}function c(e,n){if(void 0===n&&(n=0),n>=7)return{type:\"object\",value:\"[Maximum depth reached]\"};switch(typeof e){case\"string\":return{type:\"string\",value:e};case\"number\":return function(e){return{type:\"number\",value:String(e)}}(e);case\"boolean\":return function(e){return{type:\"boolean\",value:String(e)}}(e);case\"object\":try{return function(e,n){if(null===e)return{type:\"null\"};if(function(e){return e.\$&&s(e.\$)}(e))return function(e,n){return{type:\"object\",className:\"ComponentPublicInstance\",value:{properties:Object.entries(e.\$.type).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(s(e))return function(e,n){return{type:\"object\",className:\"ComponentInternalInstance\",value:{properties:Object.entries(e.type).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(function(e){return e.style&&null!=e.tagName&&null!=e.nodeName}(e))return function(e,n){return{type:\"object\",value:{properties:Object.entries(e).filter((function(e){var n=e[0];return[\"id\",\"tagName\",\"nodeName\",\"dataset\",\"offsetTop\",\"offsetLeft\",\"style\"].includes(n)})).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(function(e){return\"function\"==typeof e.getPropertyValue&&\"function\"==typeof e.setProperty&&e.\$styles}(e))return function(e,n){return{type:\"object\",value:{properties:Object.entries(e.\$styles).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(Array.isArray(e))return{type:\"object\",subType:\"array\",value:{properties:e.map((function(e,t){return function(e,n,t){var r=c(e,t);return r.name=\"\".concat(n),r}(e,t,n+1)}))}};if(e instanceof Set)return{type:\"object\",subType:\"set\",className:\"Set\",description:\"Set(\".concat(e.size,\")\"),value:{entries:Array.from(e).map((function(e){return function(e,n){return{value:c(e,n)}}(e,n+1)}))}};if(e instanceof Map)return{type:\"object\",subType:\"map\",className:\"Map\",description:\"Map(\".concat(e.size,\")\"),value:{entries:Array.from(e.entries()).map((function(e){return function(e,n){return{key:c(e[0],n),value:c(e[1],n)}}(e,n+1)}))}};if(e instanceof Promise)return{type:\"object\",subType:\"promise\",value:{properties:[]}};if(e instanceof RegExp)return{type:\"object\",subType:\"regexp\",value:String(e),className:\"Regexp\"};if(e instanceof Date)return{type:\"object\",subType:\"date\",value:String(e),className:\"Date\"};if(e instanceof Error)return{type:\"object\",subType:\"error\",value:e.message||String(e),className:e.name||\"Error\"};var t=void 0,r=e.constructor;r&&r.get\$UTSMetadata\$&&(t=r.get\$UTSMetadata\$().name);var o=Object.entries(e);(function(e){return e.modifier&&e.modifier._attribute&&e.nodeContent})(e)&&(o=o.filter((function(e){var n=e[0];return\"modifier\"!==n&&\"nodeContent\"!==n})));return{type:\"object\",className:t,value:{properties:o.map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n)}catch(e){return{type:\"object\",value:{properties:[]}}}case\"undefined\":return{type:\"undefined\"};case\"function\":return function(e){return{type:\"function\",value:\"function \".concat(e.name,\"() {}\")}}(e);case\"symbol\":return function(e){return{type:\"symbol\",value:e.description}}(e);case\"bigint\":return function(e){return{type:\"bigint\",value:String(e)}}(e)}}function s(e){return e.type&&null!=e.uid&&e.appContext}function f(e,n,t){var r=c(n,t);return r.name=e,r}var l=null,p=[],y={},g=\"---BEGIN:EXCEPTION---\",d=\"---END:EXCEPTION---\";function v(e){null!=l?l(JSON.stringify(Object.assign({type:\"console\",data:e},y))):p.push.apply(p,e)}var m=/^\\s*at\\s+[\\w/./-]+:\\d+\$/;function b(){function t(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var o=function(e,n,t){if(t||2===arguments.length)for(var r,o=0,i=n.length;o<i;o++)!r&&o in n||(r||(r=Array.prototype.slice.call(n,0,o)),r[o]=n[o]);return e.concat(r||Array.prototype.slice.call(n))}([],t,!0);if(o.length){var u=o[o.length-1];\"string\"==typeof u&&m.test(u)&&o.pop()}if(n[e].apply(n,o),\"error\"===e&&1===t.length){var c=t[0];if(\"string\"==typeof c&&c.startsWith(g)){var s=g.length,f=c.length-d.length;return void i([c.slice(s,f)])}if(c instanceof Error)return void i([c])}v([a(e,t)])}}return function(){var e=console.log,n=Symbol();try{console.log=n}catch(e){return!1}var t=console.log===n;return console.log=e,t}()?(e.forEach((function(e){console[e]=t(e)})),function(){e.forEach((function(e){console[e]=n[e]}))}):function(){}}function _(e){var n={type:\"WEB_INVOKE_APPSERVICE\",args:{data:{name:\"console\",arg:e}}};return window.__uniapp_x_postMessageToService?window.__uniapp_x_postMessageToService(n):window.__uniapp_x_.postMessageToService(JSON.stringify(n))}!function(){if(!window.__UNI_CONSOLE_WEBVIEW__){window.__UNI_CONSOLE_WEBVIEW__=!0;var e=\"[web-view]\".concat(window.__UNI_PAGE_ROUTE__?\"[\".concat(window.__UNI_PAGE_ROUTE__,\"]\"):\"\");b(),function(e,n){if(void 0===n&&(n={}),l=e,Object.assign(y,n),null!=e&&p.length>0){var t=p.slice();p.length=0,v(t)}}((function(e){_(e)}),{channel:e}),function(e,n){if(void 0===n&&(n={}),t=e,Object.assign(o,n),null!=e&&r.size>0){var a=Array.from(r);r.clear(),i(a)}}((function(e){_(e)}),{channel:e}),window.addEventListener(\"error\",(function(e){i([e.error])})),window.addEventListener(\"unhandledrejection\",(function(e){i([e])}))}}()}();"
    }
    , fun(data: String){
        socketTask?.send(SendSocketMessageOptions(data = data))
    }
    )
    return UTSPromise.resolve().then(fun(): UTSPromise<Boolean> {
        return initRuntimeSocket(hosts, port, id).then(fun(socket): Boolean {
            if (socket == null) {
                return false
            }
            socketTask = socket
            return true
        }
        )
    }
    ).`catch`(fun(): Boolean {
        return false
    }
    )
}
val runBlock2 = run {
    initRuntimeSocketService()
}
var firstBackTime: Number = 0
open class GenApp : BaseApp {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onLaunch(fun(_: OnLaunchOptions) {
            console.log("App Launch", " at App.uvue:7")
        }
        , __ins)
        onAppShow(fun(_: OnShowOptions) {
            console.log("App Show", " at App.uvue:10")
        }
        , __ins)
        onAppHide(fun() {
            console.log("App Hide", " at App.uvue:13")
        }
        , __ins)
        onLastPageBackPress(fun() {
            console.log("App LastPageBackPress", " at App.uvue:17")
            if (firstBackTime == 0) {
                uni_showToast(ShowToastOptions(title = "再按一次退出应用", position = "bottom"))
                firstBackTime = Date.now()
                setTimeout(fun(){
                    firstBackTime = 0
                }, 2000)
            } else if (Date.now() - firstBackTime < 2000) {
                firstBackTime = Date.now()
                uni_exit(null)
            }
        }
        , __ins)
        onExit(fun() {
            console.log("App Exit", " at App.uvue:34")
        }
        , __ins)
    }
    companion object {
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("uni-row" to _pS(_uM("flexDirection" to "row")), "uni-column" to _pS(_uM("flexDirection" to "column")))
            }
    }
}
val GenAppClass = CreateVueAppComponent(GenApp::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "app", name = "", inheritAttrs = true, inject = Map(), props = Map(), propsNeedCastKeys = _uA(), emits = Map(), components = Map(), styles = GenApp.styles)
}
, fun(instance): GenApp {
    return GenApp(instance)
}
)
open class FormFieldData (
    @JsonNotNull
    open var key: String,
    @JsonNotNull
    open var name: String,
    @JsonNotNull
    open var type: String,
    @JsonNotNull
    open var value: Any,
    open var isSave: Boolean? = null,
    open var condition: String? = null,
    @JsonNotNull
    open var extra: UTSJSONObject,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FormFieldData", "components/main-form/form_type.uts", 1, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return FormFieldDataReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class FormFieldDataReactiveObject : FormFieldData, IUTSReactive<FormFieldData> {
    override var __v_raw: FormFieldData
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: FormFieldData, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(key = __v_raw.key, name = __v_raw.name, type = __v_raw.type, value = __v_raw.value, isSave = __v_raw.isSave, condition = __v_raw.condition, extra = __v_raw.extra) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): FormFieldDataReactiveObject {
        return FormFieldDataReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var key: String
        get() {
            return _tRG(__v_raw, "key", __v_raw.key, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("key")) {
                return
            }
            val oldValue = __v_raw.key
            __v_raw.key = value
            _tRS(__v_raw, "key", oldValue, value)
        }
    override var name: String
        get() {
            return _tRG(__v_raw, "name", __v_raw.name, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("name")) {
                return
            }
            val oldValue = __v_raw.name
            __v_raw.name = value
            _tRS(__v_raw, "name", oldValue, value)
        }
    override var type: String
        get() {
            return _tRG(__v_raw, "type", __v_raw.type, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("type")) {
                return
            }
            val oldValue = __v_raw.type
            __v_raw.type = value
            _tRS(__v_raw, "type", oldValue, value)
        }
    override var value: Any
        get() {
            return _tRG(__v_raw, "value", __v_raw.value, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("value")) {
                return
            }
            val oldValue = __v_raw.value
            __v_raw.value = value
            _tRS(__v_raw, "value", oldValue, value)
        }
    override var isSave: Boolean?
        get() {
            return _tRG(__v_raw, "isSave", __v_raw.isSave, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("isSave")) {
                return
            }
            val oldValue = __v_raw.isSave
            __v_raw.isSave = value
            _tRS(__v_raw, "isSave", oldValue, value)
        }
    override var condition: String?
        get() {
            return _tRG(__v_raw, "condition", __v_raw.condition, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("condition")) {
                return
            }
            val oldValue = __v_raw.condition
            __v_raw.condition = value
            _tRS(__v_raw, "condition", oldValue, value)
        }
    override var extra: UTSJSONObject
        get() {
            return _tRG(__v_raw, "extra", __v_raw.extra, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("extra")) {
                return
            }
            val oldValue = __v_raw.extra
            __v_raw.extra = value
            _tRS(__v_raw, "extra", oldValue, value)
        }
}
open class FormChangeEvent (
    @JsonNotNull
    open var index: Number,
    @JsonNotNull
    open var value: Any,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FormChangeEvent", "components/main-form/form_type.uts", 10, 13)
    }
}
val GenComponentsMainFormToolsMainYearmonthPickerClass = CreateVueComponent(GenComponentsMainFormToolsMainYearmonthPicker::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormToolsMainYearmonthPicker.name, inheritAttrs = GenComponentsMainFormToolsMainYearmonthPicker.inheritAttrs, inject = GenComponentsMainFormToolsMainYearmonthPicker.inject, props = GenComponentsMainFormToolsMainYearmonthPicker.props, propsNeedCastKeys = GenComponentsMainFormToolsMainYearmonthPicker.propsNeedCastKeys, emits = GenComponentsMainFormToolsMainYearmonthPicker.emits, components = GenComponentsMainFormToolsMainYearmonthPicker.components, styles = GenComponentsMainFormToolsMainYearmonthPicker.styles)
}
, fun(instance, renderer): GenComponentsMainFormToolsMainYearmonthPicker {
    return GenComponentsMainFormToolsMainYearmonthPicker(instance)
}
)
open class ColorInfo (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("ColorInfo", "components/main-color-picker/main-color-picker.uvue", 117, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return ColorInfoReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class ColorInfoReactiveObject : ColorInfo, IUTSReactive<ColorInfo> {
    override var __v_raw: ColorInfo
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: ColorInfo, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(r = __v_raw.r, g = __v_raw.g, b = __v_raw.b) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): ColorInfoReactiveObject {
        return ColorInfoReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var r: Number
        get() {
            return _tRG(__v_raw, "r", __v_raw.r, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("r")) {
                return
            }
            val oldValue = __v_raw.r
            __v_raw.r = value
            _tRS(__v_raw, "r", oldValue, value)
        }
    override var g: Number
        get() {
            return _tRG(__v_raw, "g", __v_raw.g, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("g")) {
                return
            }
            val oldValue = __v_raw.g
            __v_raw.g = value
            _tRS(__v_raw, "g", oldValue, value)
        }
    override var b: Number
        get() {
            return _tRG(__v_raw, "b", __v_raw.b, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("b")) {
                return
            }
            val oldValue = __v_raw.b
            __v_raw.b = value
            _tRS(__v_raw, "b", oldValue, value)
        }
}
open class RGBAValues (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
    @JsonNotNull
    open var a: Number,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("RGBAValues", "components/main-color-picker/main-color-picker.uvue", 122, 7)
    }
}
open class RGBValues (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("RGBValues", "components/main-color-picker/main-color-picker.uvue", 128, 7)
    }
}
open class ColorSeries (
    @JsonNotNull
    open var name: String,
    @JsonNotNull
    open var color: String,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("ColorSeries", "components/main-color-picker/main-color-picker.uvue", 133, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return ColorSeriesReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class ColorSeriesReactiveObject : ColorSeries, IUTSReactive<ColorSeries> {
    override var __v_raw: ColorSeries
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: ColorSeries, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(name = __v_raw.name, color = __v_raw.color) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): ColorSeriesReactiveObject {
        return ColorSeriesReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var name: String
        get() {
            return _tRG(__v_raw, "name", __v_raw.name, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("name")) {
                return
            }
            val oldValue = __v_raw.name
            __v_raw.name = value
            _tRS(__v_raw, "name", oldValue, value)
        }
    override var color: String
        get() {
            return _tRG(__v_raw, "color", __v_raw.color, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("color")) {
                return
            }
            val oldValue = __v_raw.color
            __v_raw.color = value
            _tRS(__v_raw, "color", oldValue, value)
        }
}
val GenComponentsMainColorPickerMainColorPickerClass = CreateVueComponent(GenComponentsMainColorPickerMainColorPicker::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainColorPickerMainColorPicker.name, inheritAttrs = GenComponentsMainColorPickerMainColorPicker.inheritAttrs, inject = GenComponentsMainColorPickerMainColorPicker.inject, props = GenComponentsMainColorPickerMainColorPicker.props, propsNeedCastKeys = GenComponentsMainColorPickerMainColorPicker.propsNeedCastKeys, emits = GenComponentsMainColorPickerMainColorPicker.emits, components = GenComponentsMainColorPickerMainColorPicker.components, styles = GenComponentsMainColorPickerMainColorPicker.styles)
}
, fun(instance, renderer): GenComponentsMainColorPickerMainColorPicker {
    return GenComponentsMainColorPickerMainColorPicker(instance)
}
)
val GenComponentsMainFormComponentsFormContainerClass = CreateVueComponent(GenComponentsMainFormComponentsFormContainer::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormContainer.name, inheritAttrs = GenComponentsMainFormComponentsFormContainer.inheritAttrs, inject = GenComponentsMainFormComponentsFormContainer.inject, props = GenComponentsMainFormComponentsFormContainer.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormContainer.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormContainer.emits, components = GenComponentsMainFormComponentsFormContainer.components, styles = GenComponentsMainFormComponentsFormContainer.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormContainer {
    return GenComponentsMainFormComponentsFormContainer(instance)
}
)
val GenComponentsMainFormComponentsFormInputClass = CreateVueComponent(GenComponentsMainFormComponentsFormInput::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormInput.name, inheritAttrs = GenComponentsMainFormComponentsFormInput.inheritAttrs, inject = GenComponentsMainFormComponentsFormInput.inject, props = GenComponentsMainFormComponentsFormInput.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormInput.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormInput.emits, components = GenComponentsMainFormComponentsFormInput.components, styles = GenComponentsMainFormComponentsFormInput.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormInput {
    return GenComponentsMainFormComponentsFormInput(instance)
}
)
val GenComponentsMainFormComponentsFormTextareaClass = CreateVueComponent(GenComponentsMainFormComponentsFormTextarea::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormTextarea.name, inheritAttrs = GenComponentsMainFormComponentsFormTextarea.inheritAttrs, inject = GenComponentsMainFormComponentsFormTextarea.inject, props = GenComponentsMainFormComponentsFormTextarea.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormTextarea.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormTextarea.emits, components = GenComponentsMainFormComponentsFormTextarea.components, styles = GenComponentsMainFormComponentsFormTextarea.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormTextarea {
    return GenComponentsMainFormComponentsFormTextarea(instance)
}
)
val GenComponentsMainFormComponentsFormSwitchClass = CreateVueComponent(GenComponentsMainFormComponentsFormSwitch::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormSwitch.name, inheritAttrs = GenComponentsMainFormComponentsFormSwitch.inheritAttrs, inject = GenComponentsMainFormComponentsFormSwitch.inject, props = GenComponentsMainFormComponentsFormSwitch.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormSwitch.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormSwitch.emits, components = GenComponentsMainFormComponentsFormSwitch.components, styles = GenComponentsMainFormComponentsFormSwitch.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormSwitch {
    return GenComponentsMainFormComponentsFormSwitch(instance)
}
)
val GenComponentsMainFormComponentsFormSliderClass = CreateVueComponent(GenComponentsMainFormComponentsFormSlider::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormSlider.name, inheritAttrs = GenComponentsMainFormComponentsFormSlider.inheritAttrs, inject = GenComponentsMainFormComponentsFormSlider.inject, props = GenComponentsMainFormComponentsFormSlider.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormSlider.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormSlider.emits, components = GenComponentsMainFormComponentsFormSlider.components, styles = GenComponentsMainFormComponentsFormSlider.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormSlider {
    return GenComponentsMainFormComponentsFormSlider(instance)
}
)
val GenComponentsMainFormComponentsFormNumberboxClass = CreateVueComponent(GenComponentsMainFormComponentsFormNumberbox::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormNumberbox.name, inheritAttrs = GenComponentsMainFormComponentsFormNumberbox.inheritAttrs, inject = GenComponentsMainFormComponentsFormNumberbox.inject, props = GenComponentsMainFormComponentsFormNumberbox.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormNumberbox.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormNumberbox.emits, components = GenComponentsMainFormComponentsFormNumberbox.components, styles = GenComponentsMainFormComponentsFormNumberbox.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormNumberbox {
    return GenComponentsMainFormComponentsFormNumberbox(instance)
}
)
open class ColorInfo1 (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("ColorInfo", "components/main-form/tools/main-color-picker.uvue", 117, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return ColorInfo1ReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class ColorInfo1ReactiveObject : ColorInfo1, IUTSReactive<ColorInfo1> {
    override var __v_raw: ColorInfo1
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: ColorInfo1, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(r = __v_raw.r, g = __v_raw.g, b = __v_raw.b) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): ColorInfo1ReactiveObject {
        return ColorInfo1ReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var r: Number
        get() {
            return _tRG(__v_raw, "r", __v_raw.r, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("r")) {
                return
            }
            val oldValue = __v_raw.r
            __v_raw.r = value
            _tRS(__v_raw, "r", oldValue, value)
        }
    override var g: Number
        get() {
            return _tRG(__v_raw, "g", __v_raw.g, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("g")) {
                return
            }
            val oldValue = __v_raw.g
            __v_raw.g = value
            _tRS(__v_raw, "g", oldValue, value)
        }
    override var b: Number
        get() {
            return _tRG(__v_raw, "b", __v_raw.b, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("b")) {
                return
            }
            val oldValue = __v_raw.b
            __v_raw.b = value
            _tRS(__v_raw, "b", oldValue, value)
        }
}
open class RGBAValues1 (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
    @JsonNotNull
    open var a: Number,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("RGBAValues", "components/main-form/tools/main-color-picker.uvue", 122, 7)
    }
}
open class RGBValues1 (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("RGBValues", "components/main-form/tools/main-color-picker.uvue", 128, 7)
    }
}
open class ColorSeries1 (
    @JsonNotNull
    open var name: String,
    @JsonNotNull
    open var color: String,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("ColorSeries", "components/main-form/tools/main-color-picker.uvue", 133, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return ColorSeries1ReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class ColorSeries1ReactiveObject : ColorSeries1, IUTSReactive<ColorSeries1> {
    override var __v_raw: ColorSeries1
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: ColorSeries1, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(name = __v_raw.name, color = __v_raw.color) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): ColorSeries1ReactiveObject {
        return ColorSeries1ReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var name: String
        get() {
            return _tRG(__v_raw, "name", __v_raw.name, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("name")) {
                return
            }
            val oldValue = __v_raw.name
            __v_raw.name = value
            _tRS(__v_raw, "name", oldValue, value)
        }
    override var color: String
        get() {
            return _tRG(__v_raw, "color", __v_raw.color, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("color")) {
                return
            }
            val oldValue = __v_raw.color
            __v_raw.color = value
            _tRS(__v_raw, "color", oldValue, value)
        }
}
val GenComponentsMainFormToolsMainColorPickerClass = CreateVueComponent(GenComponentsMainFormToolsMainColorPicker::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormToolsMainColorPicker.name, inheritAttrs = GenComponentsMainFormToolsMainColorPicker.inheritAttrs, inject = GenComponentsMainFormToolsMainColorPicker.inject, props = GenComponentsMainFormToolsMainColorPicker.props, propsNeedCastKeys = GenComponentsMainFormToolsMainColorPicker.propsNeedCastKeys, emits = GenComponentsMainFormToolsMainColorPicker.emits, components = GenComponentsMainFormToolsMainColorPicker.components, styles = GenComponentsMainFormToolsMainColorPicker.styles)
}
, fun(instance, renderer): GenComponentsMainFormToolsMainColorPicker {
    return GenComponentsMainFormToolsMainColorPicker(instance)
}
)
val GenComponentsMainFormComponentsFormColorClass = CreateVueComponent(GenComponentsMainFormComponentsFormColor::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormColor.name, inheritAttrs = GenComponentsMainFormComponentsFormColor.inheritAttrs, inject = GenComponentsMainFormComponentsFormColor.inject, props = GenComponentsMainFormComponentsFormColor.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormColor.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormColor.emits, components = GenComponentsMainFormComponentsFormColor.components, styles = GenComponentsMainFormComponentsFormColor.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormColor {
    return GenComponentsMainFormComponentsFormColor(instance)
}
)
open class SelectOption (
    @JsonNotNull
    open var text: String,
    @JsonNotNull
    open var value: Any,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("SelectOption", "components/main-form/components/form-select.uvue", 23, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return SelectOptionReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class SelectOptionReactiveObject : SelectOption, IUTSReactive<SelectOption> {
    override var __v_raw: SelectOption
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: SelectOption, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(text = __v_raw.text, value = __v_raw.value) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): SelectOptionReactiveObject {
        return SelectOptionReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var text: String
        get() {
            return _tRG(__v_raw, "text", __v_raw.text, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("text")) {
                return
            }
            val oldValue = __v_raw.text
            __v_raw.text = value
            _tRS(__v_raw, "text", oldValue, value)
        }
    override var value: Any
        get() {
            return _tRG(__v_raw, "value", __v_raw.value, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("value")) {
                return
            }
            val oldValue = __v_raw.value
            __v_raw.value = value
            _tRS(__v_raw, "value", oldValue, value)
        }
}
val GenComponentsMainFormComponentsFormSelectClass = CreateVueComponent(GenComponentsMainFormComponentsFormSelect::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormSelect.name, inheritAttrs = GenComponentsMainFormComponentsFormSelect.inheritAttrs, inject = GenComponentsMainFormComponentsFormSelect.inject, props = GenComponentsMainFormComponentsFormSelect.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormSelect.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormSelect.emits, components = GenComponentsMainFormComponentsFormSelect.components, styles = GenComponentsMainFormComponentsFormSelect.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormSelect {
    return GenComponentsMainFormComponentsFormSelect(instance)
}
)
val GenComponentsMainFormComponentsFormYearmonthClass = CreateVueComponent(GenComponentsMainFormComponentsFormYearmonth::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormYearmonth.name, inheritAttrs = GenComponentsMainFormComponentsFormYearmonth.inheritAttrs, inject = GenComponentsMainFormComponentsFormYearmonth.inject, props = GenComponentsMainFormComponentsFormYearmonth.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormYearmonth.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormYearmonth.emits, components = GenComponentsMainFormComponentsFormYearmonth.components, styles = GenComponentsMainFormComponentsFormYearmonth.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormYearmonth {
    return GenComponentsMainFormComponentsFormYearmonth(instance)
}
)
val GenComponentsMainFormMainFormClass = CreateVueComponent(GenComponentsMainFormMainForm::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormMainForm.name, inheritAttrs = GenComponentsMainFormMainForm.inheritAttrs, inject = GenComponentsMainFormMainForm.inject, props = GenComponentsMainFormMainForm.props, propsNeedCastKeys = GenComponentsMainFormMainForm.propsNeedCastKeys, emits = GenComponentsMainFormMainForm.emits, components = GenComponentsMainFormMainForm.components, styles = GenComponentsMainFormMainForm.styles)
}
, fun(instance, renderer): GenComponentsMainFormMainForm {
    return GenComponentsMainFormMainForm(instance)
}
)
val GenPagesIndexIndexClass = CreateVueComponent(GenPagesIndexIndex::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "page", name = "", inheritAttrs = GenPagesIndexIndex.inheritAttrs, inject = GenPagesIndexIndex.inject, props = GenPagesIndexIndex.props, propsNeedCastKeys = GenPagesIndexIndex.propsNeedCastKeys, emits = GenPagesIndexIndex.emits, components = GenPagesIndexIndex.components, styles = GenPagesIndexIndex.styles)
}
, fun(instance, renderer): GenPagesIndexIndex {
    return GenPagesIndexIndex(instance, renderer)
}
)
open class LunarInfoType (
    @JsonNotNull
    open var lYear: Number,
    @JsonNotNull
    open var lMonth: Number,
    @JsonNotNull
    open var lDay: Number,
    @JsonNotNull
    open var IMonthCn: String,
    @JsonNotNull
    open var IDayCn: String,
    @JsonNotNull
    open var cYear: Number,
    @JsonNotNull
    open var cMonth: Number,
    @JsonNotNull
    open var cDay: Number,
    open var gzYear: String? = null,
    open var gzMonth: String? = null,
    open var gzDay: String? = null,
    @JsonNotNull
    open var isToday: Boolean = false,
    @JsonNotNull
    open var isLeap: Boolean = false,
    open var nWeek: Number? = null,
    open var ncWeek: String? = null,
    open var isTerm: Boolean? = null,
    open var Term: String? = null,
    open var astro: String? = null,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("LunarInfoType", "utils/calendar.uts", 2, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return LunarInfoTypeReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class LunarInfoTypeReactiveObject : LunarInfoType, IUTSReactive<LunarInfoType> {
    override var __v_raw: LunarInfoType
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: LunarInfoType, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(lYear = __v_raw.lYear, lMonth = __v_raw.lMonth, lDay = __v_raw.lDay, IMonthCn = __v_raw.IMonthCn, IDayCn = __v_raw.IDayCn, cYear = __v_raw.cYear, cMonth = __v_raw.cMonth, cDay = __v_raw.cDay, gzYear = __v_raw.gzYear, gzMonth = __v_raw.gzMonth, gzDay = __v_raw.gzDay, isToday = __v_raw.isToday, isLeap = __v_raw.isLeap, nWeek = __v_raw.nWeek, ncWeek = __v_raw.ncWeek, isTerm = __v_raw.isTerm, Term = __v_raw.Term, astro = __v_raw.astro) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): LunarInfoTypeReactiveObject {
        return LunarInfoTypeReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var lYear: Number
        get() {
            return _tRG(__v_raw, "lYear", __v_raw.lYear, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lYear")) {
                return
            }
            val oldValue = __v_raw.lYear
            __v_raw.lYear = value
            _tRS(__v_raw, "lYear", oldValue, value)
        }
    override var lMonth: Number
        get() {
            return _tRG(__v_raw, "lMonth", __v_raw.lMonth, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lMonth")) {
                return
            }
            val oldValue = __v_raw.lMonth
            __v_raw.lMonth = value
            _tRS(__v_raw, "lMonth", oldValue, value)
        }
    override var lDay: Number
        get() {
            return _tRG(__v_raw, "lDay", __v_raw.lDay, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lDay")) {
                return
            }
            val oldValue = __v_raw.lDay
            __v_raw.lDay = value
            _tRS(__v_raw, "lDay", oldValue, value)
        }
    override var IMonthCn: String
        get() {
            return _tRG(__v_raw, "IMonthCn", __v_raw.IMonthCn, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("IMonthCn")) {
                return
            }
            val oldValue = __v_raw.IMonthCn
            __v_raw.IMonthCn = value
            _tRS(__v_raw, "IMonthCn", oldValue, value)
        }
    override var IDayCn: String
        get() {
            return _tRG(__v_raw, "IDayCn", __v_raw.IDayCn, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("IDayCn")) {
                return
            }
            val oldValue = __v_raw.IDayCn
            __v_raw.IDayCn = value
            _tRS(__v_raw, "IDayCn", oldValue, value)
        }
    override var cYear: Number
        get() {
            return _tRG(__v_raw, "cYear", __v_raw.cYear, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("cYear")) {
                return
            }
            val oldValue = __v_raw.cYear
            __v_raw.cYear = value
            _tRS(__v_raw, "cYear", oldValue, value)
        }
    override var cMonth: Number
        get() {
            return _tRG(__v_raw, "cMonth", __v_raw.cMonth, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("cMonth")) {
                return
            }
            val oldValue = __v_raw.cMonth
            __v_raw.cMonth = value
            _tRS(__v_raw, "cMonth", oldValue, value)
        }
    override var cDay: Number
        get() {
            return _tRG(__v_raw, "cDay", __v_raw.cDay, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("cDay")) {
                return
            }
            val oldValue = __v_raw.cDay
            __v_raw.cDay = value
            _tRS(__v_raw, "cDay", oldValue, value)
        }
    override var gzYear: String?
        get() {
            return _tRG(__v_raw, "gzYear", __v_raw.gzYear, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("gzYear")) {
                return
            }
            val oldValue = __v_raw.gzYear
            __v_raw.gzYear = value
            _tRS(__v_raw, "gzYear", oldValue, value)
        }
    override var gzMonth: String?
        get() {
            return _tRG(__v_raw, "gzMonth", __v_raw.gzMonth, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("gzMonth")) {
                return
            }
            val oldValue = __v_raw.gzMonth
            __v_raw.gzMonth = value
            _tRS(__v_raw, "gzMonth", oldValue, value)
        }
    override var gzDay: String?
        get() {
            return _tRG(__v_raw, "gzDay", __v_raw.gzDay, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("gzDay")) {
                return
            }
            val oldValue = __v_raw.gzDay
            __v_raw.gzDay = value
            _tRS(__v_raw, "gzDay", oldValue, value)
        }
    override var isToday: Boolean
        get() {
            return _tRG(__v_raw, "isToday", __v_raw.isToday, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("isToday")) {
                return
            }
            val oldValue = __v_raw.isToday
            __v_raw.isToday = value
            _tRS(__v_raw, "isToday", oldValue, value)
        }
    override var isLeap: Boolean
        get() {
            return _tRG(__v_raw, "isLeap", __v_raw.isLeap, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("isLeap")) {
                return
            }
            val oldValue = __v_raw.isLeap
            __v_raw.isLeap = value
            _tRS(__v_raw, "isLeap", oldValue, value)
        }
    override var nWeek: Number?
        get() {
            return _tRG(__v_raw, "nWeek", __v_raw.nWeek, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("nWeek")) {
                return
            }
            val oldValue = __v_raw.nWeek
            __v_raw.nWeek = value
            _tRS(__v_raw, "nWeek", oldValue, value)
        }
    override var ncWeek: String?
        get() {
            return _tRG(__v_raw, "ncWeek", __v_raw.ncWeek, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("ncWeek")) {
                return
            }
            val oldValue = __v_raw.ncWeek
            __v_raw.ncWeek = value
            _tRS(__v_raw, "ncWeek", oldValue, value)
        }
    override var isTerm: Boolean?
        get() {
            return _tRG(__v_raw, "isTerm", __v_raw.isTerm, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("isTerm")) {
                return
            }
            val oldValue = __v_raw.isTerm
            __v_raw.isTerm = value
            _tRS(__v_raw, "isTerm", oldValue, value)
        }
    override var Term: String?
        get() {
            return _tRG(__v_raw, "Term", __v_raw.Term, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("Term")) {
                return
            }
            val oldValue = __v_raw.Term
            __v_raw.Term = value
            _tRS(__v_raw, "Term", oldValue, value)
        }
    override var astro: String?
        get() {
            return _tRG(__v_raw, "astro", __v_raw.astro, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("astro")) {
                return
            }
            val oldValue = __v_raw.astro
            __v_raw.astro = value
            _tRS(__v_raw, "astro", oldValue, value)
        }
}
open class DateType (
    @JsonNotNull
    open var fullDate: String,
    @JsonNotNull
    open var year: Number,
    @JsonNotNull
    open var month: Number,
    @JsonNotNull
    open var date: Number,
    @JsonNotNull
    open var day: Number,
    @JsonNotNull
    open var disabled: Boolean = false,
    @JsonNotNull
    open var lunar: String,
    @JsonNotNull
    open var is_today: Boolean = false,
    open var data: LunarInfoType? = null,
    @JsonNotNull
    open var lYear: Number,
    @JsonNotNull
    open var lMonth: Number,
    @JsonNotNull
    open var lDay: Number,
    @JsonNotNull
    open var lMonthCn: String,
    @JsonNotNull
    open var lDayCn: String,
    @JsonNotNull
    open var lunarDate: String,
    open var festival: String? = null,
    open var lunarFestival: String? = null,
    @JsonNotNull
    open var nWeek: Number,
    @JsonNotNull
    open var ncWeek: String,
    @JsonNotNull
    open var isTerm: Boolean = false,
    open var Term: String? = null,
    @JsonNotNull
    open var isLeap: Boolean = false,
    @JsonNotNull
    open var Animal: String,
    @JsonNotNull
    open var astro: String,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("DateType", "utils/calendar.uts", 23, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return DateTypeReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class DateTypeReactiveObject : DateType, IUTSReactive<DateType> {
    override var __v_raw: DateType
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: DateType, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(fullDate = __v_raw.fullDate, year = __v_raw.year, month = __v_raw.month, date = __v_raw.date, day = __v_raw.day, disabled = __v_raw.disabled, lunar = __v_raw.lunar, is_today = __v_raw.is_today, data = __v_raw.data, lYear = __v_raw.lYear, lMonth = __v_raw.lMonth, lDay = __v_raw.lDay, lMonthCn = __v_raw.lMonthCn, lDayCn = __v_raw.lDayCn, lunarDate = __v_raw.lunarDate, festival = __v_raw.festival, lunarFestival = __v_raw.lunarFestival, nWeek = __v_raw.nWeek, ncWeek = __v_raw.ncWeek, isTerm = __v_raw.isTerm, Term = __v_raw.Term, isLeap = __v_raw.isLeap, Animal = __v_raw.Animal, astro = __v_raw.astro) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): DateTypeReactiveObject {
        return DateTypeReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var fullDate: String
        get() {
            return _tRG(__v_raw, "fullDate", __v_raw.fullDate, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("fullDate")) {
                return
            }
            val oldValue = __v_raw.fullDate
            __v_raw.fullDate = value
            _tRS(__v_raw, "fullDate", oldValue, value)
        }
    override var year: Number
        get() {
            return _tRG(__v_raw, "year", __v_raw.year, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("year")) {
                return
            }
            val oldValue = __v_raw.year
            __v_raw.year = value
            _tRS(__v_raw, "year", oldValue, value)
        }
    override var month: Number
        get() {
            return _tRG(__v_raw, "month", __v_raw.month, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("month")) {
                return
            }
            val oldValue = __v_raw.month
            __v_raw.month = value
            _tRS(__v_raw, "month", oldValue, value)
        }
    override var date: Number
        get() {
            return _tRG(__v_raw, "date", __v_raw.date, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("date")) {
                return
            }
            val oldValue = __v_raw.date
            __v_raw.date = value
            _tRS(__v_raw, "date", oldValue, value)
        }
    override var day: Number
        get() {
            return _tRG(__v_raw, "day", __v_raw.day, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("day")) {
                return
            }
            val oldValue = __v_raw.day
            __v_raw.day = value
            _tRS(__v_raw, "day", oldValue, value)
        }
    override var disabled: Boolean
        get() {
            return _tRG(__v_raw, "disabled", __v_raw.disabled, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("disabled")) {
                return
            }
            val oldValue = __v_raw.disabled
            __v_raw.disabled = value
            _tRS(__v_raw, "disabled", oldValue, value)
        }
    override var lunar: String
        get() {
            return _tRG(__v_raw, "lunar", __v_raw.lunar, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lunar")) {
                return
            }
            val oldValue = __v_raw.lunar
            __v_raw.lunar = value
            _tRS(__v_raw, "lunar", oldValue, value)
        }
    override var is_today: Boolean
        get() {
            return _tRG(__v_raw, "is_today", __v_raw.is_today, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("is_today")) {
                return
            }
            val oldValue = __v_raw.is_today
            __v_raw.is_today = value
            _tRS(__v_raw, "is_today", oldValue, value)
        }
    override var data: LunarInfoType?
        get() {
            return _tRG(__v_raw, "data", __v_raw.data, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("data")) {
                return
            }
            val oldValue = __v_raw.data
            __v_raw.data = value
            _tRS(__v_raw, "data", oldValue, value)
        }
    override var lYear: Number
        get() {
            return _tRG(__v_raw, "lYear", __v_raw.lYear, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lYear")) {
                return
            }
            val oldValue = __v_raw.lYear
            __v_raw.lYear = value
            _tRS(__v_raw, "lYear", oldValue, value)
        }
    override var lMonth: Number
        get() {
            return _tRG(__v_raw, "lMonth", __v_raw.lMonth, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lMonth")) {
                return
            }
            val oldValue = __v_raw.lMonth
            __v_raw.lMonth = value
            _tRS(__v_raw, "lMonth", oldValue, value)
        }
    override var lDay: Number
        get() {
            return _tRG(__v_raw, "lDay", __v_raw.lDay, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lDay")) {
                return
            }
            val oldValue = __v_raw.lDay
            __v_raw.lDay = value
            _tRS(__v_raw, "lDay", oldValue, value)
        }
    override var lMonthCn: String
        get() {
            return _tRG(__v_raw, "lMonthCn", __v_raw.lMonthCn, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lMonthCn")) {
                return
            }
            val oldValue = __v_raw.lMonthCn
            __v_raw.lMonthCn = value
            _tRS(__v_raw, "lMonthCn", oldValue, value)
        }
    override var lDayCn: String
        get() {
            return _tRG(__v_raw, "lDayCn", __v_raw.lDayCn, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lDayCn")) {
                return
            }
            val oldValue = __v_raw.lDayCn
            __v_raw.lDayCn = value
            _tRS(__v_raw, "lDayCn", oldValue, value)
        }
    override var lunarDate: String
        get() {
            return _tRG(__v_raw, "lunarDate", __v_raw.lunarDate, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lunarDate")) {
                return
            }
            val oldValue = __v_raw.lunarDate
            __v_raw.lunarDate = value
            _tRS(__v_raw, "lunarDate", oldValue, value)
        }
    override var festival: String?
        get() {
            return _tRG(__v_raw, "festival", __v_raw.festival, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("festival")) {
                return
            }
            val oldValue = __v_raw.festival
            __v_raw.festival = value
            _tRS(__v_raw, "festival", oldValue, value)
        }
    override var lunarFestival: String?
        get() {
            return _tRG(__v_raw, "lunarFestival", __v_raw.lunarFestival, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lunarFestival")) {
                return
            }
            val oldValue = __v_raw.lunarFestival
            __v_raw.lunarFestival = value
            _tRS(__v_raw, "lunarFestival", oldValue, value)
        }
    override var nWeek: Number
        get() {
            return _tRG(__v_raw, "nWeek", __v_raw.nWeek, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("nWeek")) {
                return
            }
            val oldValue = __v_raw.nWeek
            __v_raw.nWeek = value
            _tRS(__v_raw, "nWeek", oldValue, value)
        }
    override var ncWeek: String
        get() {
            return _tRG(__v_raw, "ncWeek", __v_raw.ncWeek, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("ncWeek")) {
                return
            }
            val oldValue = __v_raw.ncWeek
            __v_raw.ncWeek = value
            _tRS(__v_raw, "ncWeek", oldValue, value)
        }
    override var isTerm: Boolean
        get() {
            return _tRG(__v_raw, "isTerm", __v_raw.isTerm, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("isTerm")) {
                return
            }
            val oldValue = __v_raw.isTerm
            __v_raw.isTerm = value
            _tRS(__v_raw, "isTerm", oldValue, value)
        }
    override var Term: String?
        get() {
            return _tRG(__v_raw, "Term", __v_raw.Term, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("Term")) {
                return
            }
            val oldValue = __v_raw.Term
            __v_raw.Term = value
            _tRS(__v_raw, "Term", oldValue, value)
        }
    override var isLeap: Boolean
        get() {
            return _tRG(__v_raw, "isLeap", __v_raw.isLeap, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("isLeap")) {
                return
            }
            val oldValue = __v_raw.isLeap
            __v_raw.isLeap = value
            _tRS(__v_raw, "isLeap", oldValue, value)
        }
    override var Animal: String
        get() {
            return _tRG(__v_raw, "Animal", __v_raw.Animal, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("Animal")) {
                return
            }
            val oldValue = __v_raw.Animal
            __v_raw.Animal = value
            _tRS(__v_raw, "Animal", oldValue, value)
        }
    override var astro: String
        get() {
            return _tRG(__v_raw, "astro", __v_raw.astro, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("astro")) {
                return
            }
            val oldValue = __v_raw.astro
            __v_raw.astro = value
            _tRS(__v_raw, "astro", oldValue, value)
        }
}
open class InfoType (
    @JsonNotNull
    open var lunarY: Number,
    @JsonNotNull
    open var lunarM: Number,
    @JsonNotNull
    open var lunarD: Number,
    @JsonNotNull
    open var isLeap: Boolean = false,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("InfoType", "utils/calendar.uts", 55, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return InfoTypeReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class InfoTypeReactiveObject : InfoType, IUTSReactive<InfoType> {
    override var __v_raw: InfoType
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: InfoType, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(lunarY = __v_raw.lunarY, lunarM = __v_raw.lunarM, lunarD = __v_raw.lunarD, isLeap = __v_raw.isLeap) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): InfoTypeReactiveObject {
        return InfoTypeReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var lunarY: Number
        get() {
            return _tRG(__v_raw, "lunarY", __v_raw.lunarY, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lunarY")) {
                return
            }
            val oldValue = __v_raw.lunarY
            __v_raw.lunarY = value
            _tRS(__v_raw, "lunarY", oldValue, value)
        }
    override var lunarM: Number
        get() {
            return _tRG(__v_raw, "lunarM", __v_raw.lunarM, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lunarM")) {
                return
            }
            val oldValue = __v_raw.lunarM
            __v_raw.lunarM = value
            _tRS(__v_raw, "lunarM", oldValue, value)
        }
    override var lunarD: Number
        get() {
            return _tRG(__v_raw, "lunarD", __v_raw.lunarD, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("lunarD")) {
                return
            }
            val oldValue = __v_raw.lunarD
            __v_raw.lunarD = value
            _tRS(__v_raw, "lunarD", oldValue, value)
        }
    override var isLeap: Boolean
        get() {
            return _tRG(__v_raw, "isLeap", __v_raw.isLeap, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("isLeap")) {
                return
            }
            val oldValue = __v_raw.isLeap
            __v_raw.isLeap = value
            _tRS(__v_raw, "isLeap", oldValue, value)
        }
}
open class FestivalType (
    @JsonNotNull
    open var title: String,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FestivalType", "utils/calendar.uts", 62, 6)
    }
}
val lunarYears: UTSArray<Number> = _uA(
    0x04bd8,
    0x04ae0,
    0x0a570,
    0x054d5,
    0x0d260,
    0x0d950,
    0x16554,
    0x056a0,
    0x09ad0,
    0x055d2,
    0x04ae0,
    0x0a5b6,
    0x0a4d0,
    0x0d250,
    0x1d255,
    0x0b540,
    0x0d6a0,
    0x0ada2,
    0x095b0,
    0x14977,
    0x04970,
    0x0a4b0,
    0x0b4b5,
    0x06a50,
    0x06d40,
    0x1ab54,
    0x02b60,
    0x09570,
    0x052f2,
    0x04970,
    0x06566,
    0x0d4a0,
    0x0ea50,
    0x16a95,
    0x05ad0,
    0x02b60,
    0x186e3,
    0x092e0,
    0x1c8d7,
    0x0c950,
    0x0d4a0,
    0x1d8a6,
    0x0b550,
    0x056a0,
    0x1a5b4,
    0x025d0,
    0x092d0,
    0x0d2b2,
    0x0a950,
    0x0b557,
    0x06ca0,
    0x0b550,
    0x15355,
    0x04da0,
    0x0a5b0,
    0x14573,
    0x052b0,
    0x0a9a8,
    0x0e950,
    0x06aa0,
    0x0aea6,
    0x0ab50,
    0x04b60,
    0x0aae4,
    0x0a570,
    0x05260,
    0x0f263,
    0x0d950,
    0x05b57,
    0x056a0,
    0x096d0,
    0x04dd5,
    0x04ad0,
    0x0a4d0,
    0x0d4d4,
    0x0d250,
    0x0d558,
    0x0b540,
    0x0b6a0,
    0x195a6,
    0x095b0,
    0x049b0,
    0x0a974,
    0x0a4b0,
    0x0b27a,
    0x06a50,
    0x06d40,
    0x0af46,
    0x0ab60,
    0x09570,
    0x04af5,
    0x04970,
    0x064b0,
    0x074a3,
    0x0ea50,
    0x06b58,
    0x05ac0,
    0x0ab60,
    0x096d5,
    0x092e0,
    0x0c960,
    0x0d954,
    0x0d4a0,
    0x0da50,
    0x07552,
    0x056a0,
    0x0abb7,
    0x025d0,
    0x092d0,
    0x0cab5,
    0x0a950,
    0x0b4a0,
    0x0baa4,
    0x0ad50,
    0x055d9,
    0x04ba0,
    0x0a5b0,
    0x15176,
    0x052b0,
    0x0a930,
    0x07954,
    0x06aa0,
    0x0ad50,
    0x05b52,
    0x04b60,
    0x0a6e6,
    0x0a4e0,
    0x0d260,
    0x0ea65,
    0x0d530,
    0x05aa0,
    0x076a3,
    0x096d0,
    0x04afb,
    0x04ad0,
    0x0a4d0,
    0x1d0b6,
    0x0d250,
    0x0d520,
    0x0dd45,
    0x0b5a0,
    0x056d0,
    0x055b2,
    0x049b0,
    0x0a577,
    0x0a4b0,
    0x0aa50,
    0x1b255,
    0x06d20,
    0x0ada0,
    0x14b63,
    0x09370,
    0x049f8,
    0x04970,
    0x064b0,
    0x168a6,
    0x0ea50,
    0x06b20,
    0x1a6c4,
    0x0aae0,
    0x092e0,
    0x0d2e3,
    0x0c960,
    0x0d557,
    0x0d4a0,
    0x0da50,
    0x05d55,
    0x056a0,
    0x0a6d0,
    0x055d4,
    0x052d0,
    0x0a9b8,
    0x0a950,
    0x0b4a0,
    0x0b6a6,
    0x0ad50,
    0x055a0,
    0x0aba4,
    0x0a5b0,
    0x052b0,
    0x0b273,
    0x06930,
    0x07337,
    0x06aa0,
    0x0ad50,
    0x14b55,
    0x04b60,
    0x0a570,
    0x054e4,
    0x0d160,
    0x0e968,
    0x0d520,
    0x0daa0,
    0x16aa6,
    0x056d0,
    0x04ae0,
    0x0a9d4,
    0x0a2d0,
    0x0d150,
    0x0f252,
    0x0d520
)
val N_STR_3 = _uA(
    "\u6708",
    "\u6b63",
    "\u4e8c",
    "\u4e09",
    "\u56db",
    "\u4e94",
    "\u516d",
    "\u4e03",
    "\u516b",
    "\u4e5d",
    "\u5341",
    "\u51ac",
    "\u814a"
)
val N_STR_1 = _uA(
    "\u65e5",
    "\u4e00",
    "\u4e8c",
    "\u4e09",
    "\u56db",
    "\u4e94",
    "\u516d",
    "\u4e03",
    "\u516b",
    "\u4e5d",
    "\u5341"
)
val N_STR_2 = _uA(
    "\u521d",
    "\u5341",
    "\u5eff",
    "\u5345",
    "\u95f0"
)
val Animals = _uA(
    "\u9f20",
    "\u725b",
    "\u864e",
    "\u5154",
    "\u9f99",
    "\u86c7",
    "\u9a6c",
    "\u7f8a",
    "\u7334",
    "\u9e21",
    "\u72d7",
    "\u732a"
)
val festival = Map<String, FestivalType>(_uA(
    _uA(
        "1-1",
        FestivalType(title = "元旦节")
    ),
    _uA(
        "2-14",
        FestivalType(title = "情人节")
    ),
    _uA(
        "3-8",
        FestivalType(title = "妇女节")
    ),
    _uA(
        "3-12",
        FestivalType(title = "植树节")
    ),
    _uA(
        "4-1",
        FestivalType(title = "愚人节")
    ),
    _uA(
        "4-4",
        FestivalType(title = "清明节")
    ),
    _uA(
        "5-1",
        FestivalType(title = "劳动节")
    ),
    _uA(
        "5-4",
        FestivalType(title = "青年节")
    ),
    _uA(
        "5-12",
        FestivalType(title = "护士节")
    ),
    _uA(
        "6-1",
        FestivalType(title = "儿童节")
    ),
    _uA(
        "7-1",
        FestivalType(title = "建党节")
    ),
    _uA(
        "8-1",
        FestivalType(title = "建军节")
    ),
    _uA(
        "9-10",
        FestivalType(title = "教师节")
    ),
    _uA(
        "10-1",
        FestivalType(title = "国庆节")
    ),
    _uA(
        "12-24",
        FestivalType(title = "平安夜")
    ),
    _uA(
        "12-25",
        FestivalType(title = "圣诞节")
    )
))
val lfestival = Map<String, FestivalType>(_uA(
    _uA(
        "12-30",
        FestivalType(title = "除夕")
    ),
    _uA(
        "1-1",
        FestivalType(title = "春节")
    ),
    _uA(
        "1-15",
        FestivalType(title = "元宵节")
    ),
    _uA(
        "2-2",
        FestivalType(title = "龙抬头")
    ),
    _uA(
        "5-5",
        FestivalType(title = "端午节")
    ),
    _uA(
        "7-7",
        FestivalType(title = "七夕节")
    ),
    _uA(
        "7-15",
        FestivalType(title = "中元节")
    ),
    _uA(
        "8-15",
        FestivalType(title = "中秋节")
    ),
    _uA(
        "9-9",
        FestivalType(title = "重阳节")
    ),
    _uA(
        "10-1",
        FestivalType(title = "寒衣节")
    ),
    _uA(
        "10-15",
        FestivalType(title = "下元节")
    ),
    _uA(
        "12-8",
        FestivalType(title = "腊八节")
    ),
    _uA(
        "12-23",
        FestivalType(title = "北方小年")
    ),
    _uA(
        "12-24",
        FestivalType(title = "南方小年")
    )
))
val solarTerm = _uA(
    "\u5c0f\u5bd2",
    "\u5927\u5bd2",
    "\u7acb\u6625",
    "\u96e8\u6c34",
    "\u60ca\u86f0",
    "\u6625\u5206",
    "\u6e05\u660e",
    "\u8c37\u96e8",
    "\u7acb\u590f",
    "\u5c0f\u6ee1",
    "\u8292\u79cd",
    "\u590f\u81f3",
    "\u5c0f\u6691",
    "\u5927\u6691",
    "\u7acb\u79cb",
    "\u5904\u6691",
    "\u767d\u9732",
    "\u79cb\u5206",
    "\u5bd2\u9732",
    "\u971c\u964d",
    "\u7acb\u51ac",
    "\u5c0f\u96ea",
    "\u5927\u96ea",
    "\u51ac\u81f3"
)
val sTermInfo = _uA(
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf97c3598082c95f8c965cc920f",
    "97bd0b06bdb0722c965ce1cfcc920f",
    "b027097bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf97c359801ec95f8c965cc920f",
    "97bd0b06bdb0722c965ce1cfcc920f",
    "b027097bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf97c359801ec95f8c965cc920f",
    "97bd0b06bdb0722c965ce1cfcc920f",
    "b027097bd097c36b0b6fc9274c91aa",
    "9778397bd19801ec9210c965cc920e",
    "97b6b97bd19801ec95f8c965cc920f",
    "97bd09801d98082c95f8e1cfcc920f",
    "97bd097bd097c36b0b6fc9210c8dc2",
    "9778397bd197c36c9210c9274c91aa",
    "97b6b97bd19801ec95f8c965cc920e",
    "97bd09801d98082c95f8e1cfcc920f",
    "97bd097bd097c36b0b6fc9210c8dc2",
    "9778397bd097c36c9210c9274c91aa",
    "97b6b97bd19801ec95f8c965cc920e",
    "97bcf97c3598082c95f8e1cfcc920f",
    "97bd097bd097c36b0b6fc9210c8dc2",
    "9778397bd097c36c9210c9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf97c3598082c95f8c965cc920f",
    "97bd097bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf97c3598082c95f8c965cc920f",
    "97bd097bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf97c359801ec95f8c965cc920f",
    "97bd097bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf97c359801ec95f8c965cc920f",
    "97bd097bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf97c359801ec95f8c965cc920f",
    "97bd097bd07f595b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9210c8dc2",
    "9778397bd19801ec9210c9274c920e",
    "97b6b97bd19801ec95f8c965cc920f",
    "97bd07f5307f595b0b0bc920fb0722",
    "7f0e397bd097c36b0b6fc9210c8dc2",
    "9778397bd097c36c9210c9274c920e",
    "97b6b97bd19801ec95f8c965cc920f",
    "97bd07f5307f595b0b0bc920fb0722",
    "7f0e397bd097c36b0b6fc9210c8dc2",
    "9778397bd097c36c9210c9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bd07f1487f595b0b0bc920fb0722",
    "7f0e397bd097c36b0b6fc9210c8dc2",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf7f1487f595b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf7f1487f595b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf7f1487f531b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c965cc920e",
    "97bcf7f1487f531b0b0bb0b6fb0722",
    "7f0e397bd07f595b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b97bd19801ec9210c9274c920e",
    "97bcf7f0e47f531b0b0bb0b6fb0722",
    "7f0e397bd07f595b0b0bc920fb0722",
    "9778397bd097c36b0b6fc9210c91aa",
    "97b6b97bd197c36c9210c9274c920e",
    "97bcf7f0e47f531b0b0bb0b6fb0722",
    "7f0e397bd07f595b0b0bc920fb0722",
    "9778397bd097c36b0b6fc9210c8dc2",
    "9778397bd097c36c9210c9274c920e",
    "97b6b7f0e47f531b0723b0b6fb0722",
    "7f0e37f5307f595b0b0bc920fb0722",
    "7f0e397bd097c36b0b6fc9210c8dc2",
    "9778397bd097c36b0b70c9274c91aa",
    "97b6b7f0e47f531b0723b0b6fb0721",
    "7f0e37f1487f595b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc9210c8dc2",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f595b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e397bd07f595b0b0bc920fb0722",
    "9778397bd097c36b0b6fc9274c91aa",
    "97b6b7f0e47f531b0723b0787b0721",
    "7f0e27f0e47f531b0b0bb0b6fb0722",
    "7f0e397bd07f595b0b0bc920fb0722",
    "9778397bd097c36b0b6fc9210c91aa",
    "97b6b7f0e47f149b0723b0787b0721",
    "7f0e27f0e47f531b0723b0b6fb0722",
    "7f0e397bd07f595b0b0bc920fb0722",
    "9778397bd097c36b0b6fc9210c8dc2",
    "977837f0e37f149b0723b0787b0721",
    "7f07e7f0e47f531b0723b0b6fb0722",
    "7f0e37f5307f595b0b0bc920fb0722",
    "7f0e397bd097c35b0b6fc9210c8dc2",
    "977837f0e37f14998082b0787b0721",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e37f1487f595b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc9210c8dc2",
    "977837f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc920fb0722",
    "977837f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e397bd097c35b0b6fc920fb0722",
    "977837f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e397bd07f595b0b0bc920fb0722",
    "977837f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e397bd07f595b0b0bc920fb0722",
    "977837f0e37f14998082b0787b06bd",
    "7f07e7f0e47f149b0723b0787b0721",
    "7f0e27f0e47f531b0b0bb0b6fb0722",
    "7f0e397bd07f595b0b0bc920fb0722",
    "977837f0e37f14998082b0723b06bd",
    "7f07e7f0e37f149b0723b0787b0721",
    "7f0e27f0e47f531b0723b0b6fb0722",
    "7f0e397bd07f595b0b0bc920fb0722",
    "977837f0e37f14898082b0723b02d5",
    "7ec967f0e37f14998082b0787b0721",
    "7f07e7f0e47f531b0723b0b6fb0722",
    "7f0e37f1487f595b0b0bb0b6fb0722",
    "7f0e37f0e37f14898082b0723b02d5",
    "7ec967f0e37f14998082b0787b0721",
    "7f07e7f0e47f531b0723b0b6fb0722",
    "7f0e37f1487f531b0b0bb0b6fb0722",
    "7f0e37f0e37f14898082b0723b02d5",
    "7ec967f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e37f1487f531b0b0bb0b6fb0722",
    "7f0e37f0e37f14898082b072297c35",
    "7ec967f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e37f0e37f14898082b072297c35",
    "7ec967f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e37f0e366aa89801eb072297c35",
    "7ec967f0e37f14998082b0787b06bd",
    "7f07e7f0e47f149b0723b0787b0721",
    "7f0e27f1487f531b0b0bb0b6fb0722",
    "7f0e37f0e366aa89801eb072297c35",
    "7ec967f0e37f14998082b0723b06bd",
    "7f07e7f0e47f149b0723b0787b0721",
    "7f0e27f0e47f531b0723b0b6fb0722",
    "7f0e37f0e366aa89801eb072297c35",
    "7ec967f0e37f14998082b0723b06bd",
    "7f07e7f0e37f14998083b0787b0721",
    "7f0e27f0e47f531b0723b0b6fb0722",
    "7f0e37f0e366aa89801eb072297c35",
    "7ec967f0e37f14898082b0723b02d5",
    "7f07e7f0e37f14998082b0787b0721",
    "7f07e7f0e47f531b0723b0b6fb0722",
    "7f0e36665b66aa89801e9808297c35",
    "665f67f0e37f14898082b0723b02d5",
    "7ec967f0e37f14998082b0787b0721",
    "7f07e7f0e47f531b0723b0b6fb0722",
    "7f0e36665b66a449801e9808297c35",
    "665f67f0e37f14898082b0723b02d5",
    "7ec967f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e36665b66a449801e9808297c35",
    "665f67f0e37f14898082b072297c35",
    "7ec967f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e26665b66a449801e9808297c35",
    "665f67f0e37f1489801eb072297c35",
    "7ec967f0e37f14998082b0787b06bd",
    "7f07e7f0e47f531b0723b0b6fb0721",
    "7f0e27f1487f531b0b0bb0b6fb0722"
)
open class Lunar : IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("Lunar", "utils/calendar.uts", 222, 14)
    }
    private var lunarYearDaysMap = Map<Number, Number>()
    private var lunarMonthDaysMap = Map<Number, UTSArray<Number>>()
    constructor(){}
    open fun toChinaMonth(m: Number, leap: Boolean = false): String {
        return if (leap) {
            (N_STR_3[4] + N_STR_3[m] + N_STR_3[0])
        } else {
            (N_STR_3[m] + N_STR_3[0])
        }
    }
    open fun toChinaDay(d: Number): String {
        var s: String
        when (d) {
            10 -> 
                s = "\u521d\u5341"
            20 -> 
                s = "\u4e8c\u5341"
            30 -> 
                s = "\u4e09\u5341"
            else -> 
                {
                    s = N_STR_2[Math.floor(d / 10)]
                    s += N_STR_1[d % 10]
                }
        }
        return s
    }
    open fun leapMonth(year: Number): Number {
        return lunarYears[year - 1900] and 0xF
    }
    open fun leapDays(year: Number): Number {
        if (this.leapMonth(year) > 0) {
            return if ((lunarYears[year - 1900] and 0x10000) != 0) {
                30
            } else {
                29
            }
        }
        return 0
    }
    open fun lunarMonthDays(year: Number): UTSArray<Number> {
        var monthDays = this.lunarMonthDaysMap.get(year)
        if (monthDays != null) {
            return monthDays
        }
        monthDays = _uA()
        var lunarYear = lunarYears[year - 1900]
        run {
            var i: Number = 15
            while(i >= 4){
                var monthDay = if ((lunarYear shr i and 0x1) != 0) {
                    30
                } else {
                    29
                }
                monthDays.push(monthDay)
                i--
            }
        }
        var leapM = this.leapMonth(year)
        if (leapM > 0) {
            monthDays.splice(leapM, 0, this.leapDays(year))
        }
        this.lunarMonthDaysMap.set(year, monthDays)
        return monthDays
    }
    open fun lunarYearDays(year: Number): Number {
        if (this.lunarYearDaysMap.has(year)) {
            return this.lunarYearDaysMap.get(year)!!
        }
        var num: Number = 0
        this.lunarMonthDays(year).forEach(fun(item){
            num += item
        }
        )
        this.lunarYearDaysMap.set(year, num)
        return num
    }
    open fun solar2lunar(y: Number, m: Number, d: Number): LunarInfoType {
        var moonDay = this.solar_date(y, m, d)
        var lYear = moonDay.lunarY
        var lMonth = moonDay.lunarM
        var lDay = moonDay.lunarD
        var isLeap = moonDay.isLeap
        val IMonthCn = this.toChinaMonth(lMonth, isLeap)
        val festivalKey = "" + m + "-" + d
        val solarFestival = festival.get(festivalKey)
        val lunarFestivalKey = "" + lMonth + "-" + lDay
        val lunarFestival = lfestival.get(lunarFestivalKey)
        val firstNode = this.getTerm(y, (m * 2 - 1))
        val secondNode = this.getTerm(y, (m * 2))
        var isTerm = false
        var Term: String? = null
        if (firstNode == d) {
            isTerm = true
            Term = solarTerm[m * 2 - 2]
        }
        if (secondNode == d) {
            isTerm = true
            Term = solarTerm[m * 2 - 1]
        }
        var IDayCn: String
        if (isTerm && Term != null) {
            IDayCn = Term
        } else if (solarFestival != null) {
            IDayCn = solarFestival.title
        } else if (lunarFestival != null) {
            IDayCn = lunarFestival.title
        } else if (lDay == 1) {
            IDayCn = IMonthCn
        } else {
            IDayCn = this.toChinaDay(lDay)
        }
        var isTodayObj = Date()
        var isToday = false
        if (isTodayObj.getFullYear() == y && isTodayObj.getMonth() + 1 == m && isTodayObj.getDate() == d) {
            isToday = true
        }
        val dateObj = Date(y, m - 1, d)
        var nWeek = dateObj.getDay()
        if (nWeek == 0) {
            nWeek = 7
        }
        val weekNames = _uA(
            "\u65e5",
            "\u4e00",
            "\u4e8c",
            "\u4e09",
            "\u56db",
            "\u4e94",
            "\u516d"
        )
        val cWeek = weekNames[dateObj.getDay()]
        val ncWeek = "\u661f\u671f" + cWeek
        var info = LunarInfoType(lYear = lYear, lMonth = lMonth, lDay = lDay, IMonthCn = IMonthCn, IDayCn = IDayCn, cYear = y, cMonth = m, cDay = d, isToday = isToday, isLeap = isLeap, isTerm = isTerm, Term = Term, nWeek = nWeek, ncWeek = ncWeek, astro = this.toAstro(m, d))
        return info
    }
    open fun solar_date(y: Number, m: Number, d: Number): InfoType {
        var date = Date(y, m - 1, d)
        var offset = (Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()) - Date.UTC(1901, 1, 19)) / 86400000
        var temp: Number = 0
        var i: Number
        run {
            i = 1901
            while(i < 2101 && offset > 0){
                temp = this.lunarYearDays(i)
                offset -= temp
                i++
            }
        }
        if (offset < 0) {
            offset += temp
            i--
        }
        var isLeap: Boolean = false
        var j: Number = 0
        var monthDays = this.lunarMonthDays(i)
        var leapM = this.leapMonth(i)
        if (offset > 0) {
            run {
                j = 0
                while(j < monthDays.length && offset > 0){
                    temp = monthDays[j]
                    offset -= temp
                    j++
                }
            }
            if (offset == 0) {
                j++
            }
            if (offset < 0) {
                offset += temp
            }
        } else {
            if (offset == -23) {}
        }
        if (leapM > 0) {
            if (j == leapM + 1) {
                isLeap = true
            }
            if (j >= leapM + 1) {
                j--
            }
        }
        val info = InfoType(lunarY = i, lunarM = j, lunarD = ++offset, isLeap = isLeap)
        return info
    }
    open fun getTerm(y: Number, n: Number): Number {
        if (y < 1900 || y > 2100) {
            return -1
        }
        if (n < 1 || n > 24) {
            return -1
        }
        val _table = sTermInfo[y - 1900]
        val _info = _uA(
            parseInt("0x" + _table.substring(0, 5)).toString(10),
            parseInt("0x" + _table.substring(5, 10)).toString(10),
            parseInt("0x" + _table.substring(10, 15)).toString(10),
            parseInt("0x" + _table.substring(15, 20)).toString(10),
            parseInt("0x" + _table.substring(20, 25)).toString(10),
            parseInt("0x" + _table.substring(25, 30)).toString(10)
        )
        val _calday = _uA(
            _info[0].substring(0, 1),
            _info[0].substring(1, 3),
            _info[0].substring(3, 4),
            _info[0].substring(4, 6),
            _info[1].substring(0, 1),
            _info[1].substring(1, 3),
            _info[1].substring(3, 4),
            _info[1].substring(4, 6),
            _info[2].substring(0, 1),
            _info[2].substring(1, 3),
            _info[2].substring(3, 4),
            _info[2].substring(4, 6),
            _info[3].substring(0, 1),
            _info[3].substring(1, 3),
            _info[3].substring(3, 4),
            _info[3].substring(4, 6),
            _info[4].substring(0, 1),
            _info[4].substring(1, 3),
            _info[4].substring(3, 4),
            _info[4].substring(4, 6),
            _info[5].substring(0, 1),
            _info[5].substring(1, 3),
            _info[5].substring(3, 4),
            _info[5].substring(4, 6)
        )
        return parseInt(_calday[n - 1])
    }
    open fun getAnimal(y: Number): String {
        return Animals[(y - 4) % 12]
    }
    open fun toAstro(cMonth: Number, cDay: Number): String {
        val s = "\u9b54\u7faf\u6c34\u74f6\u53cc\u9c7c\u767d\u7f8a\u91d1\u725b\u53cc\u5b50\u5de8\u87f9\u72ee\u5b50\u5904\u5973\u5929\u79e4\u5929\u874e\u5c04\u624b\u9b54\u7faf"
        val arr: UTSArray<Number> = _uA(
            20,
            19,
            21,
            21,
            21,
            22,
            23,
            23,
            23,
            23,
            22,
            22
        )
        return s.substring(cMonth * 2 - (if (cDay < arr[cMonth - 1]) {
            2
        } else {
            0
        }
        ), cMonth * 2 - (if (cDay < arr[cMonth - 1]) {
            2
        } else {
            0
        }
        ) + 2) + "\u5ea7"
    }
}
open class Calendar : IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("Calendar", "utils/calendar.uts", 528, 14)
    }
    private var lunar: Lunar
    constructor(){
        this.lunar = Lunar()
    }
    open fun getDateInfo(time: String = ""): DateType {
        val nowDate = this.getDate(time)
        val lunar = this.getlunar(nowDate.year, nowDate.month, nowDate.date)
        val item: DateType = nowDate
        item.data = lunar
        return item
    }
    open fun getWeeks(dateData: String = ""): UTSArray<UTSArray<DateType>> {
        val dateObj = this.getDate(dateData)
        val year = dateObj.year
        val month = dateObj.month
        var firstDay = Date(year, month - 1, 0).getDay()
        var currentDay = Date(year, month, 0).getDate()
        val lastMonthDays = this._getLastMonthDays(firstDay, dateObj)
        val currentMonthDys = this._currentMonthDys(currentDay, dateObj)
        val surplus = 42 - (lastMonthDays.length + currentMonthDys.length)
        val nextMonthDays = this._getNextMonthDays(surplus, dateObj)
        var days: UTSArray<DateType> = _uA()
        run {
            var i: Number = 0
            while(i < lastMonthDays.length){
                val item = lastMonthDays[i]
                days.push(item)
                i++
            }
        }
        run {
            var i: Number = 0
            while(i < currentMonthDys.length){
                val item = currentMonthDys[i]
                days.push(item)
                i++
            }
        }
        run {
            var i: Number = 0
            while(i < nextMonthDays.length){
                val item = nextMonthDays[i]
                days.push(item)
                i++
            }
        }
        var weeks: UTSArray<UTSArray<DateType>> = _uA()
        run {
            var i: Number = 0
            while(i < days.length){
                val item: UTSArray<DateType> = days.slice(i, i + 7)
                weeks.push(item)
                i += 7
            }
        }
        return weeks
    }
    open fun _getLastMonthDays(firstDay: Number, full: DateType): UTSArray<DateType> {
        var dateArr: UTSArray<DateType> = _uA()
        run {
            var i = firstDay
            while(i > 0){
                val month = full.month - 1
                val beforeDate = Date(full.year, month, -i + 1).getDate()
                var nowDate = full.year + "-" + month + "-" + beforeDate
                var item: DateType = this.getDate(nowDate)
                item.disabled = true
                dateArr.push(item)
                i--
            }
        }
        return dateArr
    }
    open fun _currentMonthDys(dateData: Number, full: DateType): UTSArray<DateType> {
        var dateArr: UTSArray<DateType> = _uA()
        run {
            var i: Number = 1
            while(i <= dateData){
                var nowDate = full.year + "-" + full.month + "-" + i
                var item: DateType = this.getDate(nowDate)
                item.disabled = false
                dateArr.push(item)
                i++
            }
        }
        return dateArr
    }
    open fun _getNextMonthDays(surplus: Number, full: DateType): UTSArray<DateType> {
        var dateArr: UTSArray<DateType> = _uA()
        run {
            var i: Number = 1
            while(i < surplus + 1){
                val month = full.month + 1
                var nowDate = full.year + "-" + month + "-" + i
                var item: DateType = this.getDate(nowDate)
                item.disabled = true
                dateArr.push(item)
                i++
            }
        }
        return dateArr
    }
    open fun getlunar(year: Number, month: Number, date: Number): LunarInfoType {
        return this.lunar.solar2lunar(year, month, date)
    }
    open fun getDate(date: String = "", AddDayCount: Number = 0, str: String = "day"): DateType {
        var dd: Date = Date()
        if (date !== "") {
            val datePart = date.split(" ")
            val dateData = datePart[0].split("-")
            val year = parseInt(dateData[0])
            val month = parseInt(dateData[1])
            val day = parseInt(dateData[2])
            dd = Date(year, month - 1, day)
        }
        when (str) {
            "day" -> 
                dd.setDate(dd.getDate() + AddDayCount)
            "month" -> 
                dd.setMonth(dd.getMonth() + AddDayCount)
            "year" -> 
                dd.setFullYear(dd.getFullYear() + AddDayCount)
        }
        val y = dd.getFullYear()
        val m = dd.getMonth() + 1
        val d = dd.getDate()
        var nowDate = y + "-" + m + "-" + d
        val lunarData = this.getlunar(y, m, d)
        val festivalKey = "" + m + "-" + d
        val solarFestival = festival.get(festivalKey)
        val lunarFestivalKey = "" + lunarData.lMonth + "-" + lunarData.lDay
        val lunarFestival = lfestival.get(lunarFestivalKey)
        val lMonthCn = this.lunar.toChinaMonth(lunarData.lMonth, lunarData.isLeap)
        val lDayCn = this.lunar.toChinaDay(lunarData.lDay)
        val lunarDate = lMonthCn + lDayCn
        val dataObj = DateType(fullDate = nowDate, year = y, month = m, date = d, day = dd.getDay() + 1, lunar = lunarData.IDayCn, is_today = lunarData.isToday, disabled = false, data = lunarData, lYear = lunarData.lYear, lMonth = lunarData.lMonth, lDay = lunarData.lDay, lMonthCn = lMonthCn, lDayCn = lDayCn, lunarDate = lunarDate, festival = solarFestival?.title ?: null, lunarFestival = lunarFestival?.title ?: null, nWeek = lunarData.nWeek ?: 1, ncWeek = lunarData.ncWeek ?: "", isTerm = lunarData.isTerm ?: false, Term = lunarData.Term, isLeap = lunarData.isLeap, Animal = this.lunar.getAnimal(lunarData.lYear), astro = lunarData.astro ?: "")
        return dataObj
    }
    open fun isCurrentMonth(year: Number, month: Number): Boolean {
        val today = Date()
        return year == today.getFullYear() && month == today.getMonth() + 1
    }
    open fun formatYearMonth(year: Number, month: Number): String {
        val monthStr = if (month < 10) {
            "0" + month
        } else {
            month.toString(10)
        }
        return "" + year + "/" + monthStr
    }
    open fun formatDate(year: Number, month: Number, date: Number): String {
        val monthStr = if (month < 10) {
            "0" + month
        } else {
            month.toString(10)
        }
        val dateStr = if (date < 10) {
            "0" + date
        } else {
            date.toString(10)
        }
        return "" + year + "-" + monthStr + "-" + dateStr
    }
}
val GenComponentsMainCalendarPickerClass = CreateVueComponent(GenComponentsMainCalendarPicker::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainCalendarPicker.name, inheritAttrs = GenComponentsMainCalendarPicker.inheritAttrs, inject = GenComponentsMainCalendarPicker.inject, props = GenComponentsMainCalendarPicker.props, propsNeedCastKeys = GenComponentsMainCalendarPicker.propsNeedCastKeys, emits = GenComponentsMainCalendarPicker.emits, components = GenComponentsMainCalendarPicker.components, styles = GenComponentsMainCalendarPicker.styles)
}
, fun(instance, renderer): GenComponentsMainCalendarPicker {
    return GenComponentsMainCalendarPicker(instance)
}
)
val GenPagesCalendarTestCalendarTestClass = CreateVueComponent(GenPagesCalendarTestCalendarTest::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "page", name = GenPagesCalendarTestCalendarTest.name, inheritAttrs = GenPagesCalendarTestCalendarTest.inheritAttrs, inject = GenPagesCalendarTestCalendarTest.inject, props = GenPagesCalendarTestCalendarTest.props, propsNeedCastKeys = GenPagesCalendarTestCalendarTest.propsNeedCastKeys, emits = GenPagesCalendarTestCalendarTest.emits, components = GenPagesCalendarTestCalendarTest.components, styles = GenPagesCalendarTestCalendarTest.styles)
}
, fun(instance, renderer): GenPagesCalendarTestCalendarTest {
    return GenPagesCalendarTestCalendarTest(instance, renderer)
}
)
fun createApp(): UTSJSONObject {
    val app = createSSRApp(GenAppClass)
    return _uO("app" to app)
}
fun main(app: IApp) {
    definePageRoutes()
    defineAppConfig()
    (createApp()["app"] as VueApp).mount(app, GenUniApp())
}
open class UniAppConfig : io.dcloud.uniapp.appframe.AppConfig {
    override var name: String = "QitTools"
    override var appid: String = "__UNI__C178CB1"
    override var versionName: String = "1.0.0"
    override var versionCode: String = "100"
    override var uniCompilerVersion: String = "4.75"
    constructor() : super() {}
}
fun definePageRoutes() {
    __uniRoutes.push(UniPageRoute(path = "pages/index/index", component = GenPagesIndexIndexClass, meta = UniPageMeta(isQuit = true), style = _uM("navigationBarTitleText" to "uni-app x")))
    __uniRoutes.push(UniPageRoute(path = "pages/calendar-test/calendar-test", component = GenPagesCalendarTestCalendarTestClass, meta = UniPageMeta(isQuit = false), style = _uM("navigationBarTitleText" to "日历")))
}
val __uniLaunchPage: Map<String, Any?> = _uM("url" to "pages/index/index", "style" to _uM("navigationBarTitleText" to "uni-app x"))
fun defineAppConfig() {
    __uniConfig.entryPagePath = "/pages/index/index"
    __uniConfig.globalStyle = _uM("navigationBarTextStyle" to "black", "navigationBarTitleText" to "uni-app x", "navigationBarBackgroundColor" to "#F8F8F8", "backgroundColor" to "#F8F8F8")
    __uniConfig.getTabBarConfig = fun(): Map<String, Any>? {
        return null
    }
    __uniConfig.tabBar = __uniConfig.getTabBarConfig()
    __uniConfig.conditionUrl = ""
    __uniConfig.uniIdRouter = _uM()
    __uniConfig.ready = true
}
open class GenUniApp : UniAppImpl() {
    open val vm: GenApp?
        get() {
            return getAppVm() as GenApp?
        }
    open val `$vm`: GenApp?
        get() {
            return getAppVm() as GenApp?
        }
}
fun getApp(): GenUniApp {
    return getUniApp() as GenUniApp
}
