// 农历信息类型
export type LunarInfoType = {
    IMonthCn: string;
    IDayCn: string;
    isToday: boolean;
};
// 日期类型
export type DateType = {
    fullDate: string;
    year: number;
    month: number;
    date: number;
    day: number;
    disabled: boolean;
    lunar: string;
    is_today: boolean;
    data?: LunarInfoType;
};
// 简化的农历类
export class Lunar {
    solar2lunar(year: number, month: number, date: number): LunarInfoType {
        // 简化实现，实际项目中可以使用完整的农历转换算法
        const today = new Date();
        const isToday = year === today.getFullYear() &&
            month === (today.getMonth() + 1) &&
            date === today.getDate();
        // 简单的农历日期显示
        const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
            '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
            '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
        const lunarDay = lunarDays[(date - 1) % 30];
        return {
            IMonthCn: '正月',
            IDayCn: lunarDay,
            isToday: isToday
        } as LunarInfoType;
    }
}
// 日历工具类
export class Calendar {
    private lunar: Lunar;
    constructor() {
        this.lunar = new Lunar();
    }
    getDateInfo(time: string = ''): DateType {
        const nowDate = this.getDate(time);
        const lunar = this.getlunar(nowDate.year, nowDate.month, nowDate.date);
        const item: DateType = nowDate;
        item.data = lunar;
        return item;
    }
    /**
     * 获取每周数据
     * @param {string} dateData
     */
    getWeeks(dateData: string = ''): Array<Array<DateType>> {
        const dateObj = this.getDate(dateData);
        const year = dateObj.year;
        const month = dateObj.month;
        let firstDay = new Date(year, month - 1, 0).getDay();
        // 获取本月天数
        let currentDay = new Date(year, month, 0).getDate();
        // 上个月末尾几天
        const lastMonthDays = this._getLastMonthDays(firstDay, dateObj);
        // 本月天数
        const currentMonthDys = this._currentMonthDys(currentDay, dateObj);
        // 本月剩余天数
        const surplus = 42 - (lastMonthDays.length + currentMonthDys.length);
        // 下个月开始几天
        const nextMonthDays = this._getNextMonthDays(surplus, dateObj);
        // 本月所有日期格子合并
        let days: Array<DateType> = [];
        for (let i = 0; i < lastMonthDays.length; i++) {
            const item = lastMonthDays[i];
            days.push(item);
        }
        for (let i = 0; i < currentMonthDys.length; i++) {
            const item = currentMonthDys[i];
            days.push(item);
        }
        for (let i = 0; i < nextMonthDays.length; i++) {
            const item = nextMonthDays[i];
            days.push(item);
        }
        let weeks: Array<Array<DateType>> = [];
        // 拼接数组  上个月开始几天 + 本月天数+ 下个月开始几天
        for (let i = 0; i < days.length; i += 7) {
            const item: Array<DateType> = days.slice(i, i + 7);
            weeks.push(item);
        }
        return weeks;
    }
    /**
     * 获取上月剩余天数
     */
    _getLastMonthDays(firstDay: number, full: DateType): Array<DateType> {
        let dateArr: Array<DateType> = [];
        for (let i = firstDay; i > 0; i--) {
            const month = full.month - 1;
            const beforeDate = new Date(full.year, month, -i + 1).getDate();
            let nowDate = full.year + '-' + month + '-' + beforeDate;
            let item: DateType = this.getDate(nowDate);
            item.disabled = true;
            dateArr.push(item);
        }
        return dateArr;
    }
    /**
     * 获取本月天数
     */
    _currentMonthDys(dateData: number, full: DateType): Array<DateType> {
        let dateArr: Array<DateType> = [];
        for (let i = 1; i <= dateData; i++) {
            let nowDate = full.year + '-' + full.month + '-' + i;
            let item: DateType = this.getDate(nowDate);
            item.disabled = false;
            dateArr.push(item);
        }
        return dateArr;
    }
    /**
     * 获取下月天数
     */
    _getNextMonthDays(surplus: number, full: DateType): Array<DateType> {
        let dateArr: Array<DateType> = [];
        for (let i = 1; i < surplus + 1; i++) {
            const month = full.month + 1;
            let nowDate = full.year + '-' + month + '-' + i;
            let item: DateType = this.getDate(nowDate);
            item.disabled = true;
            dateArr.push(item);
        }
        return dateArr;
    }
    /**
     * 计算阴历日期显示
     */
    getlunar(year: number, month: number, date: number): LunarInfoType {
        return this.lunar.solar2lunar(year, month, date);
    }
    /**
     * 获取任意时间
     */
    getDate(date: string = '', AddDayCount: number = 0, str: string = 'day'): DateType {
        let dd: Date = new Date();
        if (date !== '') {
            const datePart = date.split(" ");
            const dateData = datePart[0].split("-");
            const year = parseInt(dateData[0]);
            const month = parseInt(dateData[1]);
            const day = parseInt(dateData[2]);
            dd = new Date(year, month - 1, day);
        }
        switch (str) {
            case 'day':
                dd.setDate(dd.getDate() + AddDayCount);
                break;
            case 'month':
                dd.setMonth(dd.getMonth() + AddDayCount);
                break;
            case 'year':
                dd.setFullYear(dd.getFullYear() + AddDayCount);
                break;
        }
        const y = dd.getFullYear();
        const m = dd.getMonth() + 1;
        const d = dd.getDate();
        let nowDate = y + '-' + m + '-' + d;
        const lunarData = this.getlunar(y, m, d);
        const dataObj: DateType = {
            fullDate: nowDate,
            year: y,
            month: m,
            date: d,
            day: dd.getDay() + 1,
            lunar: lunarData.IDayCn,
            is_today: lunarData.isToday,
            disabled: false
        };
        return dataObj;
    }
    /**
     * 判断是否为当前月份
     */
    isCurrentMonth(year: number, month: number): boolean {
        const today = new Date();
        return year === today.getFullYear() && month === today.getMonth() + 1;
    }
    /**
     * 格式化年月显示 (xxxx/xx)
     */
    formatYearMonth(year: number, month: number): string {
        const monthStr = month < 10 ? '0' + month : month.toString(10);
        return `${year}/${monthStr}`;
    }
    /**
     * 格式化日期为 YYYY-MM-DD
     */
    formatDate(year: number, month: number, date: number): string {
        const monthStr = month < 10 ? '0' + month : month.toString(10);
        const dateStr = date < 10 ? '0' + date : date.toString(10);
        return `${year}-${monthStr}-${dateStr}`;
    }
}
//# sourceMappingURL=calendar.uts.map